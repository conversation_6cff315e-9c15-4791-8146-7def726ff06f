const axios = require('axios');
const sharp = require('sharp');
const fs = require('fs').promises;
const path = require('path');
const printerService = require('../services/printerService');
const queueManager = require('../services/queueManager');
const logger = require('../utils/logger');

class PrintController {
  /**
   * Конвертировать любую imgbb ссылку в прямую ссылку на изображение
   */
  convertImgbbUrl(originalUrl) {
    try {
      logger.info(`Конвертируем imgbb URL: ${originalUrl}`);

      // Если это уже прямая ссылка на изображение, возвращаем как есть
      if (originalUrl.includes('i.ibb.co/') && (originalUrl.includes('.jpg') || originalUrl.includes('.png') || originalUrl.includes('.gif'))) {
        logger.info(`URL уже является прямой ссылкой: ${originalUrl}`);
        return originalUrl;
      }

      // Если это ссылка на страницу просмотра (ibb.co/XXXXX)
      if (originalUrl.includes('ibb.co/') && !originalUrl.includes('i.ibb.co/')) {
        // Извлекаем ID из URL
        const match = originalUrl.match(/ibb\.co\/([a-zA-Z0-9]+)/);
        if (match && match[1]) {
          const imageId = match[1];
          // Пробуем разные варианты прямых ссылок
          const possibleUrls = [
            `https://i.ibb.co/${imageId}.jpg`,
            `https://i.ibb.co/${imageId}.png`,
            `https://i.ibb.co/${imageId}.gif`,
            `https://i.ibb.co/${imageId}/image.jpg`,
            `https://i.ibb.co/${imageId}/image.png`
          ];

          logger.info(`Найден ID изображения: ${imageId}, пробуем варианты: ${possibleUrls.join(', ')}`);
          return possibleUrls; // Возвращаем массив для попытки каждого
        }
      }

      // Если не удалось конвертировать, возвращаем оригинальный URL
      logger.warn(`Не удалось конвертировать URL: ${originalUrl}`);
      return originalUrl;

    } catch (error) {
      logger.error('Ошибка конвертации imgbb URL:', error);
      return originalUrl;
    }
  }

  /**
   * Попытка скачать изображение с несколькими URL (для imgbb)
   */
  async downloadImageWithFallback(urls) {
    const urlsToTry = Array.isArray(urls) ? urls : [urls];

    for (let i = 0; i < urlsToTry.length; i++) {
      const url = urlsToTry[i];
      logger.info(`Попытка ${i + 1}/${urlsToTry.length}: скачиваем ${url}`);

      try {
        const response = await axios({
          method: 'GET',
          url: url,
          responseType: 'arraybuffer',
          timeout: 30000,
          headers: {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'image/*,*/*;q=0.8',
            'Referer': 'https://imgbb.com/'
          },
          maxRedirects: 5
        });

        logger.info(`Успех! Ответ сервера: статус ${response.status}, размер: ${response.data?.byteLength || 0} байт`);

        if (response.status === 200 && response.data && response.data.byteLength > 0) {
          const imageBuffer = Buffer.from(response.data);
          logger.info(`Изображение успешно скачано с URL: ${url}, размер: ${imageBuffer.length} байт`);
          return { buffer: imageBuffer, successUrl: url };
        }

      } catch (error) {
        logger.warn(`Попытка ${i + 1} неудачна для ${url}:`, error.message);

        if (i === urlsToTry.length - 1) {
          // Это была последняя попытка
          throw new Error(`Не удалось скачать изображение ни с одного URL. Последняя ошибка: ${error.message}`);
        }
      }
    }
  }

  /**
   * Печать изображения с imgbb
   */
  async printFromImgbb(req, res) {
    try {
      const { imageUrl, format = '4x6', userId, copies = 1 } = req.body;

      logger.info(`=== НАЧАЛО ПЕЧАТИ ===`);
      logger.info(`Запрос на печать: ${imageUrl}, формат: ${format}, копий: ${copies}, пользователь: ${userId}`);

      // Конвертируем imgbb URL в прямые ссылки НАПРЯМУЮ
      logger.info(`Конвертируем imgbb URL: ${imageUrl}`);

      let urlsToTry = [imageUrl]; // По умолчанию пробуем оригинальный URL

      // Если это ссылка на страницу просмотра imgbb (ibb.co/XXXXX)
      if (imageUrl.includes('ibb.co/') && !imageUrl.includes('i.ibb.co/')) {
        const match = imageUrl.match(/ibb\.co\/([a-zA-Z0-9]+)/);
        if (match && match[1]) {
          const imageId = match[1];
          urlsToTry = [
            `https://i.ibb.co/${imageId}.jpg`,
            `https://i.ibb.co/${imageId}.png`,
            `https://i.ibb.co/${imageId}.gif`,
            `https://i.ibb.co/${imageId}/image.jpg`,
            `https://i.ibb.co/${imageId}/image.png`,
            imageUrl // И оригинальный URL как fallback
          ];
          logger.info(`Найден ID изображения: ${imageId}, пробуем варианты: ${urlsToTry.join(', ')}`);
        }
      }

      logger.info(`URL для попыток скачивания:`, urlsToTry);

      // Скачиваем изображение с fallback для imgbb НАПРЯМУЮ
      logger.info(`Шаг 1: Скачиваем изображение с поддержкой fallback`);

      let imageBuffer;
      let successUrl;

      for (let i = 0; i < urlsToTry.length; i++) {
        const url = urlsToTry[i];
        logger.info(`Попытка ${i + 1}/${urlsToTry.length}: скачиваем ${url}`);

        try {
          const response = await axios({
            method: 'GET',
            url: url,
            responseType: 'arraybuffer',
            timeout: 30000,
            headers: {
              'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
              'Accept': 'image/*,*/*;q=0.8',
              'Referer': 'https://imgbb.com/'
            },
            maxRedirects: 5
          });

          logger.info(`Успех! Ответ сервера: статус ${response.status}, размер: ${response.data?.byteLength || 0} байт`);

          if (response.status === 200 && response.data && response.data.byteLength > 0) {
            imageBuffer = Buffer.from(response.data);
            successUrl = url;
            logger.info(`Изображение успешно скачано с URL: ${url}, размер: ${imageBuffer.length} байт`);
            break; // Успех! Выходим из цикла
          }

        } catch (error) {
          logger.warn(`Попытка ${i + 1} неудачна для ${url}:`, error.message);

          if (i === urlsToTry.length - 1) {
            // Это была последняя попытка
            let errorMessage = 'Не удалось скачать изображение ни с одного URL';

            if (error.response) {
              const status = error.response.status;
              if (status === 404) {
                errorMessage = 'Изображение не найдено (404). Проверьте URL или попробуйте загрузить новое изображение на imgbb.';
              } else if (status === 403) {
                errorMessage = 'Доступ к изображению запрещен (403).';
              } else {
                errorMessage = `Ошибка HTTP ${status}: ${error.response.statusText}`;
              }
            }

            throw new Error(errorMessage);
          }
        }
      }

      if (!imageBuffer) {
        throw new Error('Не удалось скачать изображение ни с одного URL');
      }

      logger.info(`Изображение успешно скачано с URL: ${successUrl}, размер: ${imageBuffer.length} байт`);

      // Сохраняем изображение напрямую БЕЗ обработки Sharp
      logger.info(`Шаг 2: Сохраняем изображение напрямую (БЕЗ Sharp)`);

      // Определяем расширение файла из URL (более умно)
      let fileExtension = 'jpg'; // по умолчанию

      try {
        // Убираем параметры запроса из URL
        const cleanUrl = imageUrl.split('?')[0];
        const urlParts = cleanUrl.split('.');
        const extension = urlParts[urlParts.length - 1].toLowerCase();

        // Поддерживаемые расширения
        const validExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'tiff', 'svg'];

        if (validExtensions.includes(extension)) {
          fileExtension = extension;
        } else {
          // Если расширение не определено, пробуем определить по URL
          if (imageUrl.includes('.png')) fileExtension = 'png';
          else if (imageUrl.includes('.gif')) fileExtension = 'gif';
          else if (imageUrl.includes('.bmp')) fileExtension = 'bmp';
          else if (imageUrl.includes('.webp')) fileExtension = 'webp';
          else fileExtension = 'jpg'; // по умолчанию
        }

        logger.info(`Определено расширение файла: ${fileExtension} из URL: ${cleanUrl}`);
      } catch (error) {
        logger.warn('Не удалось определить расширение, используем jpg:', error.message);
        fileExtension = 'jpg';
      }

      // Сохраняем файл напрямую
      const tempDir = path.join(__dirname, '../temp');

      // Создаем папку temp если её нет
      try {
        await fs.access(tempDir);
      } catch {
        await fs.mkdir(tempDir, { recursive: true });
      }

      const fileName = `print_imgbb_${userId || 'anonymous'}_${Date.now()}.${fileExtension}`;
      const tempFilePath = path.join(tempDir, fileName);

      await fs.writeFile(tempFilePath, imageBuffer);
      logger.info(`Изображение сохранено напрямую: ${tempFilePath}, размер: ${imageBuffer.length} байт`);

      // Добавляем в очередь печати вместо прямой печати
      logger.info(`Шаг 4: Добавляем в очередь печати`);
      const printJobId = queueManager.addJob({
        type: 'image',
        filePath: tempFilePath,
        format,
        copies,
        userId,
        imageUrl,
        priority: copies > 3 ? 2 : 1 // Высокий приоритет для больших заказов
      });
      logger.info(`Задание добавлено в очередь с ID: ${printJobId}`);

      // НЕ удаляем временный файл сразу - подождем пока печать завершится
      // await this.cleanupTempFile(tempFilePath);

      logger.info(`=== ПЕЧАТЬ УСПЕШНО ОТПРАВЛЕНА ===`);

      // Вычисляем примерное время печати НАПРЯМУЮ
      const timePerPrint = {
        '4x6': 12.4, // секунд согласно спецификации DNP DS-RX1HS
        '6x8': 18,
        '5x7': 15,
        '2x6': 10
      };
      const baseTime = timePerPrint[format] || timePerPrint['4x6'];
      const estimatedTime = Math.ceil(baseTime * copies);

      res.json({
        success: true,
        message: 'Изображение отправлено на печать',
        printJobId,
        format,
        copies,
        estimatedTime,
        tempFilePath // для отладки
      });

    } catch (error) {
      logger.error('=== ОШИБКА ПЕЧАТИ ===', error);
      res.status(500).json({
        success: false,
        error: 'Ошибка при печати изображения',
        details: error.message,
        stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
      });
    }
  }

  /**
   * Получить статус принтера
   */
  async getPrinterStatus(req, res) {
    try {
      const status = await printerService.getStatus();
      res.json({
        success: true,
        status
      });
    } catch (error) {
      logger.error('Ошибка получения статуса принтера:', error);
      res.status(500).json({
        success: false,
        error: 'Не удалось получить статус принтера'
      });
    }
  }

  /**
   * Получить очередь печати
   */
  async getPrintQueue(req, res) {
    try {
      const queue = await printerService.getQueue();
      res.json({
        success: true,
        queue
      });
    } catch (error) {
      logger.error('Ошибка получения очереди печати:', error);
      res.status(500).json({
        success: false,
        error: 'Не удалось получить очередь печати'
      });
    }
  }

  /**
   * Получить список доступных принтеров
   */
  async getAvailablePrinters(req, res) {
    try {
      const printers = await printerService.getAvailablePrinters();
      res.json({
        success: true,
        printers
      });
    } catch (error) {
      logger.error('Ошибка получения списка принтеров:', error);
      res.status(500).json({
        success: false,
        error: 'Не удалось получить список принтеров',
        details: error.message
      });
    }
  }

  /**
   * Тестовая печать черно-белого прямоугольника
   */
  async printTestImage(req, res) {
    try {
      const { format = '4x6', copies = 1, userId = 'test' } = req.body;

      logger.info(`=== ТЕСТОВАЯ ПЕЧАТЬ ===`);
      logger.info(`Создаем тестовое изображение: формат ${format}, копий: ${copies}`);

      // Создаем тестовое HTML изображение НАПРЯМУЮ
      logger.info(`Создаем тестовое изображение для формата ${format}`);

      // Создаем простой HTML файл который можно распечатать
      const htmlContent = `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Тестовая печать</title>
    <style>
        @page {
            size: ${format === '4x6' ? '6in 4in' : format === '6x8' ? '8in 6in' : format === '5x7' ? '7in 5in' : '6in 2in'};
            margin: 0.5in;
        }
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: white;
        }
        .test-page {
            border: 3px solid black;
            padding: 20px;
            height: 90%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
        }
        h1 {
            font-size: 24px;
            margin-bottom: 20px;
            color: black;
        }
        .info {
            font-size: 16px;
            margin: 10px 0;
            color: #333;
        }
        .corners {
            position: absolute;
            width: 20px;
            height: 20px;
            background: black;
        }
        .corner-tl { top: 10px; left: 10px; }
        .corner-tr { top: 10px; right: 10px; }
        .corner-bl { bottom: 10px; left: 10px; }
        .corner-br { bottom: 10px; right: 10px; }
    </style>
</head>
<body>
    <div class="test-page">
        <div class="corners corner-tl"></div>
        <div class="corners corner-tr"></div>
        <div class="corners corner-bl"></div>
        <div class="corners corner-br"></div>
        
        <h1>ТЕСТОВАЯ ПЕЧАТЬ</h1>
        <div class="info">Формат: ${format}</div>
        <div class="info">Moscow 2030 Avatar Backend</div>
        <div class="info">${new Date().toLocaleString('ru-RU')}</div>
        <div class="info">Node.js Printer Test</div>
    </div>
</body>
</html>`;

      const testImage = Buffer.from(htmlContent, 'utf8');
      logger.info(`HTML тестовая страница создана: ${testImage.length} байт`);

      // Сохраняем временный файл как HTML НАПРЯМУЮ
      const tempDir = path.join(__dirname, '../temp');

      // Создаем папку temp если её нет
      try {
        await fs.access(tempDir);
      } catch {
        await fs.mkdir(tempDir, { recursive: true });
      }

      const fileName = `print_test_${userId}_${Date.now()}.html`;
      const tempFilePath = path.join(tempDir, fileName);

      await fs.writeFile(tempFilePath, testImage);
      logger.info(`Тестовый HTML файл сохранен: ${tempFilePath}`);

      // Отправляем на печать
      const printJobId = await printerService.printImage(tempFilePath, {
        format,
        copies,
        userId: `test_${userId}`
      });
      logger.info(`Тестовое задание печати создано с ID: ${printJobId}`);

      logger.info(`=== ТЕСТОВАЯ ПЕЧАТЬ ОТПРАВЛЕНА ===`);

      // Вычисляем примерное время печати НАПРЯМУЮ
      const timePerPrint = {
        '4x6': 12.4, // секунд согласно спецификации DNP DS-RX1HS
        '6x8': 18,
        '5x7': 15,
        '2x6': 10
      };
      const baseTime = timePerPrint[format] || timePerPrint['4x6'];
      const estimatedTime = Math.ceil(baseTime * copies);

      res.json({
        success: true,
        message: 'Тестовое изображение отправлено на печать',
        printJobId,
        format,
        copies,
        estimatedTime,
        testImage: true,
        filePath: tempFilePath
      });

    } catch (error) {
      logger.error('=== ОШИБКА ТЕСТОВОЙ ПЕЧАТИ ===', error);
      res.status(500).json({
        success: false,
        error: 'Ошибка при тестовой печати',
        details: error.message,
        stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
      });
    }
  }

  /**
   * СУПЕР ПРОСТАЯ тестовая печать - только текст
   */
  async printSimpleText(req, res) {
    try {
      const { text = 'ТЕСТОВАЯ ПЕЧАТЬ', copies = 1, userId = 'simple' } = req.body;

      logger.info(`=== ПРОСТАЯ ТЕКСТОВАЯ ПЕЧАТЬ ===`);
      logger.info(`Текст: ${text}, копий: ${copies}`);

      // Создаем простой текстовый файл
      const textContent = `
ТЕСТОВАЯ ПЕЧАТЬ
===============

Текст: ${text}
Время: ${new Date().toLocaleString('ru-RU')}
Пользователь: ${userId}
Копий: ${copies}

Moscow 2030 Avatar Backend
Node.js Printer Test

===============
КОНЕЦ ТЕСТА
`;

      // Сохраняем как текстовый файл напрямую
      const tempDir = path.join(__dirname, '../temp');

      // Создаем папку temp если её нет
      try {
        await fs.access(tempDir);
      } catch {
        await fs.mkdir(tempDir, { recursive: true });
      }

      const fileName = `print_simple_${userId}_${Date.now()}.txt`;
      const tempFilePath = path.join(tempDir, fileName);

      await fs.writeFile(tempFilePath, textContent, 'utf8');
      logger.info(`Простой текстовый файл сохранен: ${tempFilePath}`);

      // Отправляем на печать
      const printJobId = await printerService.printImage(tempFilePath, {
        format: 'text',
        copies,
        userId: `simple_${userId}`
      });
      logger.info(`Простое задание печати создано с ID: ${printJobId}`);

      logger.info(`=== ПРОСТАЯ ПЕЧАТЬ ОТПРАВЛЕНА ===`);

      res.json({
        success: true,
        message: 'Простой текст отправлен на печать',
        printJobId,
        text,
        copies,
        filePath: tempFilePath,
        simpleText: true
      });

    } catch (error) {
      logger.error('=== ОШИБКА ПРОСТОЙ ПЕЧАТИ ===', error);
      res.status(500).json({
        success: false,
        error: 'Ошибка при простой печати',
        details: error.message,
        stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
      });
    }
  }

  /**
   * Создать ПРОСТОЕ тестовое изображение БЕЗ Sharp - используем готовый файл
   */
  async createSimpleTestImage(format) {
    try {
      logger.info(`Создаем тестовое изображение для формата ${format}`);

      // Создаем простой HTML файл который можно распечатать
      const htmlContent = `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Тестовая печать</title>
    <style>
        @page {
            size: ${format === '4x6' ? '6in 4in' : format === '6x8' ? '8in 6in' : format === '5x7' ? '7in 5in' : '6in 2in'};
            margin: 0.5in;
        }
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: white;
        }
        .test-page {
            border: 3px solid black;
            padding: 20px;
            height: 90%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
        }
        h1 {
            font-size: 24px;
            margin-bottom: 20px;
            color: black;
        }
        .info {
            font-size: 16px;
            margin: 10px 0;
            color: #333;
        }
        .corners {
            position: absolute;
            width: 20px;
            height: 20px;
            background: black;
        }
        .corner-tl { top: 10px; left: 10px; }
        .corner-tr { top: 10px; right: 10px; }
        .corner-bl { bottom: 10px; left: 10px; }
        .corner-br { bottom: 10px; right: 10px; }
    </style>
</head>
<body>
    <div class="test-page">
        <div class="corners corner-tl"></div>
        <div class="corners corner-tr"></div>
        <div class="corners corner-bl"></div>
        <div class="corners corner-br"></div>
        
        <h1>ТЕСТОВАЯ ПЕЧАТЬ</h1>
        <div class="info">Формат: ${format}</div>
        <div class="info">Moscow 2030 Avatar Backend</div>
        <div class="info">${new Date().toLocaleString('ru-RU')}</div>
        <div class="info">Node.js Printer Test</div>
    </div>
</body>
</html>`;

      logger.info(`HTML тестовая страница создана`);
      return Buffer.from(htmlContent, 'utf8');

    } catch (error) {
      logger.error('Ошибка создания HTML тестовой страницы:', error);
      throw new Error(`Не удалось создать тестовую страницу: ${error.message}`);
    }
  }

  /**
   * Печать файла напрямую (альтернативный способ)
   */
  async printDirectFile(req, res) {
    try {
      const { filePath, format = '4x6', copies = 1, userId = 'direct' } = req.body;

      logger.info(`=== ПРЯМАЯ ПЕЧАТЬ ФАЙЛА ===`);
      logger.info(`Файл: ${filePath}, формат: ${format}, копий: ${copies}`);

      // Проверяем что файл существует
      const fs = require('fs').promises;
      try {
        await fs.access(filePath);
      } catch {
        throw new Error('Файл не найден');
      }

      // Отправляем на печать напрямую
      const printJobId = await printerService.printImage(filePath, {
        format,
        copies,
        userId: `direct_${userId}`
      });

      logger.info(`=== ПРЯМАЯ ПЕЧАТЬ ОТПРАВЛЕНА ===`);

      // Вычисляем примерное время печати НАПРЯМУЮ
      const timePerPrint = {
        '4x6': 12.4, // секунд согласно спецификации DNP DS-RX1HS
        '6x8': 18,
        '5x7': 15,
        '2x6': 10
      };
      const baseTime = timePerPrint[format] || timePerPrint['4x6'];
      const estimatedTime = Math.ceil(baseTime * copies);

      res.json({
        success: true,
        message: 'Файл отправлен на печать',
        printJobId,
        format,
        copies,
        filePath,
        estimatedTime
      });

    } catch (error) {
      logger.error('=== ОШИБКА ПРЯМОЙ ПЕЧАТИ ===', error);
      res.status(500).json({
        success: false,
        error: 'Ошибка при прямой печати файла',
        details: error.message
      });
    }
  }

  /**
   * Печать через системную команду (альтернативный способ)
   */
  async printSystemCommand(req, res) {
    try {
      const { command, description = 'Системная команда' } = req.body;

      logger.info(`=== СИСТЕМНАЯ КОМАНДА ПЕЧАТИ ===`);
      logger.info(`Команда: ${command}`);

      const { exec } = require('child_process');
      const { promisify } = require('util');
      const execAsync = promisify(exec);

      const { stdout, stderr } = await execAsync(command, { timeout: 30000 });

      logger.info(`Stdout: ${stdout}`);
      if (stderr) logger.warn(`Stderr: ${stderr}`);

      logger.info(`=== СИСТЕМНАЯ КОМАНДА ВЫПОЛНЕНА ===`);

      res.json({
        success: true,
        message: 'Системная команда выполнена',
        command,
        description,
        stdout,
        stderr
      });

    } catch (error) {
      logger.error('=== ОШИБКА СИСТЕМНОЙ КОМАНДЫ ===', error);
      res.status(500).json({
        success: false,
        error: 'Ошибка выполнения системной команды',
        details: error.message
      });
    }
  }

  /**
   * Создать тестовое черно-белое изображение
   */
  async createTestImage(format) {
    const formatSettings = {
      '4x6': { width: 1800, height: 1200 },
      '6x8': { width: 2400, height: 1800 },
      '5x7': { width: 2100, height: 1500 },
      '2x6': { width: 1800, height: 600 }
    };

    const settings = formatSettings[format] || formatSettings['4x6'];

    try {
      logger.info(`Создаем тестовое изображение ${settings.width}x${settings.height}`);

      // Создаем простое черно-белое изображение без SVG
      const testBuffer = await sharp({
        create: {
          width: settings.width,
          height: settings.height,
          channels: 3,
          background: { r: 255, g: 255, b: 255 } // белый фон
        }
      })
        .composite([
          // Черная рамка (верх)
          {
            input: {
              create: {
                width: settings.width - 100,
                height: 20,
                channels: 3,
                background: { r: 0, g: 0, b: 0 }
              }
            },
            top: 50,
            left: 50
          },
          // Черная рамка (низ)
          {
            input: {
              create: {
                width: settings.width - 100,
                height: 20,
                channels: 3,
                background: { r: 0, g: 0, b: 0 }
              }
            },
            top: settings.height - 70,
            left: 50
          },
          // Черная рамка (лево)
          {
            input: {
              create: {
                width: 20,
                height: settings.height - 100,
                channels: 3,
                background: { r: 0, g: 0, b: 0 }
              }
            },
            top: 50,
            left: 50
          },
          // Черная рамка (право)
          {
            input: {
              create: {
                width: 20,
                height: settings.height - 100,
                channels: 3,
                background: { r: 0, g: 0, b: 0 }
              }
            },
            top: 50,
            left: settings.width - 70
          },
          // Черные квадраты по углам
          {
            input: {
              create: {
                width: 100,
                height: 100,
                channels: 3,
                background: { r: 0, g: 0, b: 0 }
              }
            },
            top: 100,
            left: 100
          },
          {
            input: {
              create: {
                width: 100,
                height: 100,
                channels: 3,
                background: { r: 0, g: 0, b: 0 }
              }
            },
            top: 100,
            left: settings.width - 200
          },
          {
            input: {
              create: {
                width: 100,
                height: 100,
                channels: 3,
                background: { r: 0, g: 0, b: 0 }
              }
            },
            top: settings.height - 200,
            left: 100
          },
          {
            input: {
              create: {
                width: 100,
                height: 100,
                channels: 3,
                background: { r: 0, g: 0, b: 0 }
              }
            },
            top: settings.height - 200,
            left: settings.width - 200
          },
          // Центральный прямоугольник
          {
            input: {
              create: {
                width: Math.floor(settings.width / 2),
                height: Math.floor(settings.height / 3),
                channels: 3,
                background: { r: 200, g: 200, b: 200 } // серый
              }
            },
            top: Math.floor(settings.height / 3),
            left: Math.floor(settings.width / 4)
          }
        ])
        .jpeg({
          quality: 95,
          progressive: false
        })
        .toBuffer();

      logger.info(`Тестовое изображение создано успешно: ${testBuffer.length} байт`);
      return testBuffer;

    } catch (error) {
      logger.error('Ошибка создания тестового изображения:', error);

      // Fallback - создаем самое простое изображение
      try {
        logger.info('Создаем упрощенное тестовое изображение...');
        const simpleBuffer = await sharp({
          create: {
            width: settings.width,
            height: settings.height,
            channels: 3,
            background: { r: 240, g: 240, b: 240 } // светло-серый фон
          }
        })
          .composite([
            // Один черный квадрат в центре
            {
              input: {
                create: {
                  width: 200,
                  height: 200,
                  channels: 3,
                  background: { r: 0, g: 0, b: 0 }
                }
              },
              top: Math.floor((settings.height - 200) / 2),
              left: Math.floor((settings.width - 200) / 2)
            }
          ])
          .jpeg({
            quality: 95,
            progressive: false
          })
          .toBuffer();

        logger.info(`Упрощенное тестовое изображение создано: ${simpleBuffer.length} байт`);
        return simpleBuffer;

      } catch (fallbackError) {
        logger.error('Ошибка создания упрощенного изображения:', fallbackError);
        throw new Error(`Не удалось создать тестовое изображение: ${error.message}`);
      }
    }
  }

  /**
   * Скачать изображение с imgbb
   */
  async downloadImage(imageUrl) {
    try {
      logger.info(`Начинаем скачивание изображения: ${imageUrl}`);

      const response = await axios({
        method: 'GET',
        url: imageUrl,
        responseType: 'arraybuffer',
        timeout: 30000,
        headers: {
          'User-Agent': 'Moscow2030-Avatar-Backend/1.0',
          'Accept': 'image/*'
        },
        maxRedirects: 5
      });

      logger.info(`Ответ сервера: статус ${response.status}, размер: ${response.data?.byteLength || 0} байт`);

      if (response.status !== 200) {
        throw new Error(`HTTP ${response.status}: Не удалось скачать изображение`);
      }

      if (!response.data || response.data.byteLength === 0) {
        throw new Error('Получен пустой ответ от сервера');
      }

      const buffer = Buffer.from(response.data);
      logger.info(`Изображение успешно скачано: ${buffer.length} байт`);

      return buffer;
    } catch (error) {
      logger.error(`Ошибка скачивания изображения ${imageUrl}:`, error);
      if (error.code === 'ENOTFOUND') {
        throw new Error('Не удалось найти сервер изображения. Проверьте URL.');
      } else if (error.code === 'ETIMEDOUT') {
        throw new Error('Превышено время ожидания при скачивании изображения.');
      } else {
        throw new Error(`Не удалось скачать изображение: ${error.message}`);
      }
    }
  }

  /**
   * Обработать изображение для печати
   */
  async processImageForPrint(imageBuffer, format) {
    const formatSettings = {
      '4x6': { width: 1800, height: 1200, dpi: 300 },
      '6x8': { width: 2400, height: 1800, dpi: 300 },
      '5x7': { width: 2100, height: 1500, dpi: 300 },
      '2x6': { width: 1800, height: 600, dpi: 300 }
    };

    const settings = formatSettings[format] || formatSettings['4x6'];

    try {
      logger.info(`Начинаем обработку изображения: ${imageBuffer.length} байт`);
      logger.info(`Целевой размер: ${settings.width}x${settings.height}, DPI: ${settings.dpi}`);

      // Проверяем что imageBuffer не пустой
      if (!imageBuffer || imageBuffer.length === 0) {
        throw new Error('Пустой буфер изображения');
      }

      // Создаем Sharp объект с дополнительными опциями
      const sharpInstance = sharp(imageBuffer, {
        failOnError: false,
        limitInputPixels: false
      });

      // Получаем метаданные изображения
      const metadata = await sharpInstance.metadata();
      logger.info(`Исходное изображение: ${metadata.width}x${metadata.height}, формат: ${metadata.format}`);

      // Обрабатываем изображение
      const processedBuffer = await sharpInstance
        .resize(settings.width, settings.height, {
          fit: 'cover',
          position: 'center',
          withoutEnlargement: false
        })
        .jpeg({
          quality: 95,
          progressive: false,
          mozjpeg: true
        })
        .toBuffer();

      logger.info(`Изображение обработано успешно: ${processedBuffer.length} байт`);
      return processedBuffer;

    } catch (error) {
      logger.error('Ошибка обработки изображения:', error);
      logger.error('Stack trace:', error.stack);

      // Fallback - возвращаем оригинальное изображение если обработка не удалась
      logger.warn('Используем оригинальное изображение без обработки');
      return imageBuffer;
    }
  }

  /**
   * Сохранить временный файл
   */
  async saveTempFile(imageBuffer, userId, isHtml = false, customExtension = null) {
    const tempDir = path.join(__dirname, '../temp');

    // Создаем папку temp если её нет
    try {
      await fs.access(tempDir);
    } catch {
      await fs.mkdir(tempDir, { recursive: true });
    }

    let extension = 'jpg';
    if (customExtension) {
      extension = customExtension;
    } else if (isHtml) {
      extension = 'html';
    }

    const fileName = `print_${userId || 'anonymous'}_${Date.now()}.${extension}`;
    const filePath = path.join(tempDir, fileName);

    await fs.writeFile(filePath, imageBuffer);
    return filePath;
  }

  /**
   * Удалить временный файл
   */
  async cleanupTempFile(filePath) {
    try {
      await fs.unlink(filePath);
      logger.info(`Временный файл удален: ${filePath}`);
    } catch (error) {
      logger.warn(`Не удалось удалить временный файл ${filePath}:`, error);
    }
  }

  /**
   * Получить примерное время печати
   */
  getEstimatedPrintTime(format, copies) {
    const timePerPrint = {
      '4x6': 12.4, // секунд согласно спецификации DNP DS-RX1HS
      '6x8': 18,
      '5x7': 15,
      '2x6': 10
    };

    const baseTime = timePerPrint[format] || timePerPrint['4x6'];
    return Math.ceil(baseTime * copies);
  }
}

module.exports = new PrintController();
  async getQueueStatus(req, res) {
    try {
      const queueStatus = queueManager.getQueueStatus();
      
      res.json({
        success: true,
        queue: queueStatus
      });
    } catch (error) {
      logger.error('Ошибка получения статуса очереди:', error);
      res.status(500).json({
        success: false,
        error: 'Не удалось получить статус очереди',
        details: error.message
      });
    }
  }

  /**
   * Обновить настройки очереди
   */
  async updateQueueSettings(req, res) {
    try {
      const { maxConcurrent } = req.body;
      
      if (!maxConcurrent || maxConcurrent < 1 || maxConcurrent > 10) {
        return res.status(400).json({
          success: false,
          error: 'maxConcurrent должен быть от 1 до 10'
        });
      }

      queueManager.updateSettings({ maxConcurrent });
      
      logger.info(`Настройки очереди обновлены: maxConcurrent = ${maxConcurrent}`);
      
      res.json({
        success: true,
        message: 'Настройки очереди обновлены',
        settings: {
          maxConcurrent
        }
      });
    } catch (error) {
      logger.error('Ошибка обновления настроек очереди:', error);
      res.status(500).json({
        success: false,
        error: 'Не удалось обновить настройки очереди',
        details: error.message
      });
    }
  }

  /**
   * Очистить историю очереди
   */
  async cleanupQueue(req, res) {
    try {
      queueManager.cleanup();
      
      logger.info('История очереди очищена');
      
      res.json({
        success: true,
        message: 'История очереди очищена'
      });
    } catch (error) {
      logger.error('Ошибка очистки очереди:', error);
      res.status(500).json({
        success: false,
        error: 'Не удалось очистить историю очереди',
        details: error.message
      });
    }
  }

  /**
   * Получить информацию о задании
   */
  async getJobInfo(req, res) {
    try {
      const { jobId } = req.params;
      
      const job = queueManager.getJobById(jobId);
      
      if (!job) {
        return res.status(404).json({
          success: false,
          error: 'Задание не найдено'
        });
      }

      res.json({
        success: true,
        job: {
          id: job.id,
          type: job.type,
          status: job.status,
          userId: job.userId,
          format: job.format,
          copies: job.copies,
          priority: job.priority,
          retries: job.retries,
          maxRetries: job.maxRetries,
          createdAt: job.createdAt,
          startedAt: job.startedAt,
          completedAt: job.completedAt,
          failedAt: job.failedAt,
          cancelledAt: job.cancelledAt,
          lastError: job.lastError,
          duration: job.completedAt ? job.completedAt - job.startedAt : 
                   job.startedAt ? new Date() - job.startedAt : null
        }
      });
    } catch (error) {
      logger.error('Ошибка получения информации о задании:', error);
      res.status(500).json({
        success: false,
        error: 'Не удалось получить информацию о задании',
        details: error.message
      });
    }
  }

  /**
   * Отменить задание
   */
  async cancelJob(req, res) {
    try {
      const { jobId } = req.params;
      
      const cancelled = queueManager.cancelJob(jobId);
      
      if (!cancelled) {
        return res.status(400).json({
          success: false,
          error: 'Задание не может быть отменено (возможно, уже обрабатывается или завершено)'
        });
      }

      logger.info(`Задание отменено: ${jobId}`);
      
      res.json({
        success: true,
        message: 'Задание отменено',
        jobId
      });
    } catch (error) {
      logger.error('Ошибка отмены задания:', error);
      res.status(500).json({
        success: false,
        error: 'Не удалось отменить задание',
        details: error.message
      });
    }
  }