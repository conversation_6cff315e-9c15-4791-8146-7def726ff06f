{"name": "moscow2030-avatar-backend", "version": "1.0.0", "description": "Backend для проекта Аватар - печать изображений с imgbb", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "dev:3002": "cross-env PORT=3002 nodemon server.js", "test": "jest"}, "dependencies": {"express": "^4.18.2", "axios": "^1.6.0", "multer": "^1.4.5-lts.1", "sharp": "^0.32.6", "cors": "^2.8.5", "helmet": "^7.1.0", "dotenv": "^16.3.1", "winston": "^3.11.0", "joi": "^17.11.0", "node-printer": "^1.0.0"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.7.0"}, "keywords": ["avatar", "printing", "imgbb", "dnp", "moscow2030"], "author": "Moscow 2030 Team", "license": "MIT"}