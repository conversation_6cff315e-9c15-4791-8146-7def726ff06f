const API_BASE = 'http://localhost:3002/api';

// Утилиты
function showLoading(elementId) {
    const element = document.getElementById(elementId);
    if (element) {
        element.innerHTML = '<span class="loading"></span>';
    }
}

function hideLoading(elementId) {
    const element = document.getElementById(elementId);
    if (element) {
        element.innerHTML = '';
    }
}

function showResponse(elementId, data, type = 'info') {
    const element = document.getElementById(elementId);
    if (element) {
        element.className = `response ${type}`;
        element.textContent = JSON.stringify(data, null, 2);
    }
}

function logToConsole(message, data = null) {
    console.log(`[Avatar Print] ${message}`, data);
}

// API вызовы с отладкой
async function checkHealth() {
    logToConsole('Проверяем здоровье сервера...');
    showLoading('health-loading');
    try {
        const url = `${API_BASE.replace('/api', '')}/health`;
        logToConsole('URL запроса:', url);
        
        const response = await fetch(url);
        const data = await response.json();
        
        logToConsole('Ответ сервера:', data);
        showResponse('status-response', data, 'success');
    } catch (error) {
        logToConsole('Ошибка подключения к серверу:', error);
        showResponse('status-response', { 
            error: error.message,
            details: 'Убедитесь что сервер запущен на порту 3002'
        }, 'error');
    } finally {
        hideLoading('health-loading');
    }
}

async function checkPrinterStatus() {
    logToConsole('Проверяем статус принтера...');
    showLoading('printer-loading');
    try {
        const url = `${API_BASE}/print/status`;
        logToConsole('URL запроса:', url);
        
        const response = await fetch(url);
        const data = await response.json();
        
        logToConsole('Статус принтера:', data);
        showResponse('status-response', data, response.ok ? 'success' : 'error');
    } catch (error) {
        logToConsole('Ошибка получения статуса принтера:', error);
        showResponse('status-response', { error: error.message }, 'error');
    } finally {
        hideLoading('printer-loading');
    }
}

async function checkPrinters() {
    logToConsole('Проверяем список принтеров...');
    showLoading('printers-loading');
    try {
        const response = await fetch(`${API_BASE}/print/printers`);
        const data = await response.json();
        logToConsole('Список принтеров:', data);
        showResponse('printers-response', data, response.ok ? 'info' : 'error');
    } catch (error) {
        logToConsole('Ошибка получения принтеров:', error);
        showResponse('printers-response', { error: error.message }, 'error');
    } finally {
        hideLoading('printers-loading');
    }
}

async function printImage() {
    const imageUrl = document.getElementById('imageUrl').value;
    const format = document.getElementById('format').value;
    const copies = parseInt(document.getElementById('copies').value);
    const userId = document.getElementById('userId').value;

    logToConsole('Параметры печати:', { imageUrl, format, copies, userId });

    if (!imageUrl) {
        showResponse('print-response', { error: 'Введите URL изображения' }, 'error');
        return;
    }

    // Проверяем что URL начинается с http/https
    if (!imageUrl.startsWith('http://') && !imageUrl.startsWith('https://')) {
        showResponse('print-response', { error: 'URL должен начинаться с http:// или https://' }, 'error');
        return;
    }

    showLoading('print-loading');
    
    try {
        const requestBody = {
            imageUrl,
            format,
            copies
        };

        if (userId) {
            requestBody.userId = userId;
        }

        logToConsole('Отправляем запрос на печать:', requestBody);

        const response = await fetch(`${API_BASE}/print/imgbb`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestBody)
        });

        const data = await response.json();
        logToConsole('Ответ печати:', data);
        showResponse('print-response', data, response.ok ? 'success' : 'error');
        
        // Если печать успешна, обновляем очередь
        if (response.ok) {
            setTimeout(checkQueue, 1000);
        }
    } catch (error) {
        logToConsole('Ошибка печати:', error);
        showResponse('print-response', { error: error.message }, 'error');
    } finally {
        hideLoading('print-loading');
    }
}

async function checkQueue() {
    logToConsole('Проверяем очередь печати...');
    showLoading('queue-loading');
    try {
        const response = await fetch(`${API_BASE}/print/queue`);
        const data = await response.json();
        logToConsole('Ответ очереди:', data);
        showResponse('queue-response', data, response.ok ? 'info' : 'error');
    } catch (error) {
        logToConsole('Ошибка очереди:', error);
        showResponse('queue-response', { error: error.message }, 'error');
    } finally {
        hideLoading('queue-loading');
    }
}

async function printTestImage() {
    const format = document.getElementById('testFormat').value;
    const copies = parseInt(document.getElementById('testCopies').value);

    logToConsole('Параметры тестовой печати:', { format, copies });

    showLoading('test-loading');
    
    try {
        const requestBody = {
            format,
            copies,
            userId: 'test_user'
        };

        logToConsole('Отправляем запрос на тестовую печать:', requestBody);

        const response = await fetch(`${API_BASE}/print/test`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestBody)
        });

        const data = await response.json();
        logToConsole('Ответ тестовой печати:', data);
        showResponse('test-response', data, response.ok ? 'success' : 'error');
        
        // Если печать успешна, обновляем очередь
        if (response.ok) {
            setTimeout(checkQueue, 1000);
        }
    } catch (error) {
        logToConsole('Ошибка тестовой печати:', error);
        showResponse('test-response', { error: error.message }, 'error');
    } finally {
        hideLoading('test-loading');
    }
}

// Прямая печать файла
async function printDirectFile() {
    const filePath = document.getElementById('directFilePath').value;
    const format = document.getElementById('directFormat').value;

    logToConsole('Параметры прямой печати:', { filePath, format });

    if (!filePath) {
        showResponse('direct-response', { error: 'Введите путь к файлу' }, 'error');
        return;
    }

    showLoading('direct-loading');
    
    try {
        const requestBody = {
            filePath,
            format,
            copies: 1,
            userId: 'direct_user'
        };

        logToConsole('Отправляем запрос на прямую печать:', requestBody);

        const response = await fetch(`${API_BASE}/print/direct`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestBody)
        });

        const data = await response.json();
        logToConsole('Ответ прямой печати:', data);
        showResponse('direct-response', data, response.ok ? 'success' : 'error');
        
        if (response.ok) {
            setTimeout(checkQueue, 1000);
        }
    } catch (error) {
        logToConsole('Ошибка прямой печати:', error);
        showResponse('direct-response', { error: error.message }, 'error');
    } finally {
        hideLoading('direct-loading');
    }
}

// Выполнение системной команды
async function executeSystemCommand() {
    const command = document.getElementById('systemCommand').value;
    const description = document.getElementById('commandDescription').value;

    logToConsole('Параметры системной команды:', { command, description });

    if (!command) {
        showResponse('command-response', { error: 'Введите команду' }, 'error');
        return;
    }

    showLoading('command-loading');
    
    try {
        const requestBody = {
            command,
            description: description || 'Пользовательская команда'
        };

        logToConsole('Отправляем системную команду:', requestBody);

        const response = await fetch(`${API_BASE}/print/command`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestBody)
        });

        const data = await response.json();
        logToConsole('Ответ системной команды:', data);
        showResponse('command-response', data, response.ok ? 'success' : 'error');
        
    } catch (error) {
        logToConsole('Ошибка системной команды:', error);
        showResponse('command-response', { error: error.message }, 'error');
    } finally {
        hideLoading('command-loading');
    }
}

// Простая текстовая печать
async function printSimpleText() {
    const text = document.getElementById('simpleText').value;
    const copies = parseInt(document.getElementById('simpleCopies').value);

    logToConsole('Параметры простой текстовой печати:', { text, copies });

    if (!text) {
        showResponse('test-response', { error: 'Введите текст для печати' }, 'error');
        return;
    }

    showLoading('simple-loading');
    
    try {
        const requestBody = {
            text,
            copies,
            userId: 'simple_user'
        };

        logToConsole('Отправляем запрос на простую текстовую печать:', requestBody);

        const response = await fetch(`${API_BASE}/print/simple`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestBody)
        });

        const data = await response.json();
        logToConsole('Ответ простой текстовой печати:', data);
        showResponse('test-response', data, response.ok ? 'success' : 'error');
        
        // Если печать успешна, обновляем очередь
        if (response.ok) {
            setTimeout(checkQueue, 1000);
        }
    } catch (error) {
        logToConsole('Ошибка простой текстовой печати:', error);
        showResponse('test-response', { error: error.message }, 'error');
    } finally {
        hideLoading('simple-loading');
    }
}

// Функция для использования тестового URL
function useTestUrl(url) {
    document.getElementById('imageUrl').value = url;
    logToConsole('Установлен тестовый URL:', url);
    
    // Показываем уведомление
    showResponse('print-response', { 
        message: 'Тестовый URL установлен! Теперь нажми "Отправить на печать"',
        url: url 
    }, 'info');
}

// Быстрые команды
async function quickCommand(command) {
    logToConsole('Выполняем быструю команду:', command);
    
    try {
        const requestBody = {
            command: `powershell -Command "${command}"`,
            description: `Быстрая команда: ${command}`
        };

        logToConsole('Отправляем быструю команду:', requestBody);

        const response = await fetch(`${API_BASE}/print/command`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestBody)
        });

        const data = await response.json();
        logToConsole('Ответ быстрой команды:', data);
        showResponse('quick-response', data, response.ok ? 'success' : 'error');
        
    } catch (error) {
        logToConsole('Ошибка быстрой команды:', error);
        showResponse('quick-response', { error: error.message }, 'error');
    }
}

// Управление очередью печати
async function checkQueueStatus() {
    logToConsole('Проверяем статус очереди...');
    showLoading('queue-status-loading');
    try {
        const response = await fetch(`${API_BASE}/print/queue-status`);
        const data = await response.json();
        logToConsole('Статус очереди:', data);
        
        // Форматируем ответ для лучшего отображения
        if (data.success && data.queue) {
            const formatted = {
                "📊 Статистика": {
                    "Всего заданий": data.queue.stats.totalJobs,
                    "Завершено": data.queue.stats.completedJobs,
                    "Провалено": data.queue.stats.failedJobs,
                    "Успешность": data.queue.stats.successRate,
                    "Время работы": Math.floor(data.queue.stats.uptime / 1000) + " сек"
                },
                "🔄 Текущее состояние": {
                    "В очереди": data.queue.queue.waiting,
                    "Обрабатывается": data.queue.queue.processing,
                    "Завершено": data.queue.queue.completed,
                    "Провалено": data.queue.queue.failed
                },
                "⚙️ Настройки": {
                    "Макс. параллельных": data.queue.settings.maxConcurrent,
                    "Обработка активна": data.queue.settings.isProcessing ? "ДА" : "НЕТ"
                },
                "📋 Активные задания": data.queue.jobs.processing,
                "✅ Последние завершенные": data.queue.jobs.recentCompleted.slice(0, 3),
                "❌ Последние провалившиеся": data.queue.jobs.recentFailed.slice(0, 3)
            };
            showResponse('queue-status-response', formatted, 'info');
        } else {
            showResponse('queue-status-response', data, response.ok ? 'info' : 'error');
        }
    } catch (error) {
        logToConsole('Ошибка получения статуса очереди:', error);
        showResponse('queue-status-response', { error: error.message }, 'error');
    } finally {
        hideLoading('queue-status-loading');
    }
}

async function updateQueueSettings() {
    const maxConcurrent = parseInt(document.getElementById('maxConcurrent').value);
    
    logToConsole('Обновляем настройки очереди:', { maxConcurrent });
    showLoading('settings-loading');
    
    try {
        const response = await fetch(`${API_BASE}/print/queue-settings`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ maxConcurrent })
        });

        const data = await response.json();
        logToConsole('Ответ обновления настроек:', data);
        showResponse('settings-response', data, response.ok ? 'success' : 'error');
        
        // Обновляем статус очереди после изменения настроек
        if (response.ok) {
            setTimeout(checkQueueStatus, 1000);
        }
    } catch (error) {
        logToConsole('Ошибка обновления настроек:', error);
        showResponse('settings-response', { error: error.message }, 'error');
    } finally {
        hideLoading('settings-loading');
    }
}

async function cleanupQueue() {
    logToConsole('Очищаем историю очереди...');
    showLoading('cleanup-loading');
    
    try {
        const response = await fetch(`${API_BASE}/print/queue-cleanup`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        });

        const data = await response.json();
        logToConsole('Ответ очистки:', data);
        showResponse('cleanup-response', data, response.ok ? 'success' : 'error');
        
        // Обновляем статус очереди после очистки
        if (response.ok) {
            setTimeout(checkQueueStatus, 1000);
        }
    } catch (error) {
        logToConsole('Ошибка очистки:', error);
        showResponse('cleanup-response', { error: error.message }, 'error');
    } finally {
        hideLoading('cleanup-loading');
    }
}

// Авто-обновление статуса очереди
let autoRefreshInterval = null;
let autoRefreshEnabled = false;

function autoRefreshToggle() {
    const btn = document.getElementById('auto-refresh-btn');
    
    if (autoRefreshEnabled) {
        // Выключаем авто-обновление
        clearInterval(autoRefreshInterval);
        autoRefreshInterval = null;
        autoRefreshEnabled = false;
        btn.textContent = '🔄 Авто-обновление: ВЫКЛ';
        btn.style.background = 'linear-gradient(135deg, #28a745 0%, #1e7e34 100%)';
        logToConsole('Авто-обновление выключено');
    } else {
        // Включаем авто-обновление
        autoRefreshInterval = setInterval(() => {
            checkQueueStatus();
        }, 3000); // Обновляем каждые 3 секунды
        
        autoRefreshEnabled = true;
        btn.textContent = '🔄 Авто-обновление: ВКЛ';
        btn.style.background = 'linear-gradient(135deg, #dc3545 0%, #c82333 100%)';
        logToConsole('Авто-обновление включено (каждые 3 сек)');
        
        // Сразу обновляем статус
        checkQueueStatus();
    }
}

// Получить информацию о конкретном задании
async function getJobInfo(jobId) {
    logToConsole('Получаем информацию о задании:', jobId);
    
    try {
        const response = await fetch(`${API_BASE}/print/job/${jobId}`);
        const data = await response.json();
        logToConsole('Информация о задании:', data);
        return data;
    } catch (error) {
        logToConsole('Ошибка получения информации о задании:', error);
        return { success: false, error: error.message };
    }
}

// Отменить задание
async function cancelJob(jobId) {
    logToConsole('Отменяем задание:', jobId);
    
    try {
        const response = await fetch(`${API_BASE}/print/job/${jobId}`, {
            method: 'DELETE'
        });
        const data = await response.json();
        logToConsole('Результат отмены:', data);
        
        // Обновляем статус очереди после отмены
        if (data.success) {
            setTimeout(checkQueueStatus, 1000);
        }
        
        return data;
    } catch (error) {
        logToConsole('Ошибка отмены задания:', error);
        return { success: false, error: error.message };
    }
}

// Автоматическая проверка при загрузке страницы
window.onload = function() {
    logToConsole('Страница загружена, проверяем сервер...');
    checkHealth();
    
    // Также проверим принтеры
    setTimeout(() => {
        checkPrinters();
    }, 1000);
};