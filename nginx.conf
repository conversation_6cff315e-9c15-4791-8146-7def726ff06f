# nginx конфигурация для Moscow 2030 Avatar Backend
# Скопируй этот файл в C:\nginx\conf\sites\moscow2030-avatar.conf

# Upstream для Node.js приложения
upstream moscow2030_backend {
    server 127.0.0.1:3002;
    keepalive 32;
}

# HTTP сервер
server {
    listen 80;
    server_name localhost;
    
    # Логи
    access_log C:/nginx/logs/moscow2030-access.log;
    error_log C:/nginx/logs/moscow2030-error.log;
    
    # Основная локация - проксирование к Node.js
    location / {
        proxy_pass http://moscow2030_backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }
    
    # API эндпоинты с увеличенным таймаутом
    location /api/ {
        proxy_pass http://moscow2030_backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        client_max_body_size 50M;
    }
    
    # Rate limiting для API
    location /api/print/ {
        limit_req zone=api burst=20 nodelay;
        proxy_pass http://moscow2030_backend;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        client_max_body_size 50M;
    }
    
    # Health check
    location /health {
        proxy_pass http://moscow2030_backend;
        access_log off;
    }
    
    # Блокировка доступа к служебным файлам
    location ~ /\. {
        deny all;
    }
    
    location ~ \.(env|log)$ {
        deny all;
    }
}

# Rate limiting зона (добавь в основной nginx.conf в секцию http)
# limit_req_zone $binary_remote_addr zone=api:10m rate=10r/m;