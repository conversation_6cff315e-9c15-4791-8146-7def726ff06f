const Joi = require('joi');
const logger = require('../utils/logger');

const printRequestSchema = Joi.object({
  imageUrl: Joi.string()
    .uri()
    .required()
    .messages({
      'string.uri': 'Некорректный URL изображения',
      'any.required': 'URL изображения обязателен'
    }),
  
  format: Joi.string()
    .valid('4x6', '6x8', '5x7', '2x6')
    .default('4x6')
    .messages({
      'any.only': 'Поддерживаемые форматы: 4x6, 6x8, 5x7, 2x6'
    }),
  
  copies: Joi.number()
    .integer()
    .min(1)
    .max(5)
    .default(1)
    .messages({
      'number.min': 'Минимум 1 копия',
      'number.max': 'Максимум 5 копий за раз',
      'number.integer': 'Количество копий должно быть целым числом'
    }),
  
  userId: Joi.string()
    .alphanum()
    .min(3)
    .max(50)
    .optional()
    .messages({
      'string.alphanum': 'ID пользователя может содержать только буквы и цифры',
      'string.min': 'ID пользователя минимум 3 символа',
      'string.max': 'ID пользователя максимум 50 символов'
    })
});

const validatePrintRequest = (req, res, next) => {
  try {
    logger.info('Валидация запроса печати:', req.body);

    const { error, value } = printRequestSchema.validate(req.body, {
      abortEarly: false,
      stripUnknown: true
    });

    if (error) {
      const errorMessages = error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message
      }));

      logger.warn('Ошибка валидации запроса печати:', errorMessages);

      return res.status(400).json({
        success: false,
        error: 'Ошибка валидации данных',
        details: errorMessages
      });
    }

    // Дополнительная проверка URL - проверяем что это похоже на изображение
    if (!value.imageUrl) {
      logger.warn('Пустой URL:', value.imageUrl);
      return res.status(400).json({
        success: false,
        error: 'URL изображения не может быть пустым',
        receivedUrl: value.imageUrl
      });
    }

    // Проверяем что URL начинается с http/https
    if (!value.imageUrl.startsWith('http://') && !value.imageUrl.startsWith('https://')) {
      logger.warn('Некорректный протокол URL:', value.imageUrl);
      return res.status(400).json({
        success: false,
        error: 'URL должен начинаться с http:// или https://',
        receivedUrl: value.imageUrl
      });
    }

    logger.info('Валидация прошла успешно:', value);
    req.body = value;
    next();

  } catch (validationError) {
    logger.error('Критическая ошибка валидации:', validationError);
    return res.status(500).json({
      success: false,
      error: 'Внутренняя ошибка валидации',
      details: validationError.message
    });
  }
};

module.exports = validatePrintRequest;