# Moscow 2030 Avatar Backend - Скрипт управления
param(
    [Parameter(Mandatory=$true)]
    [ValidateSet("start", "stop", "restart", "status", "logs", "update")]
    [string]$Action
)

$appPath = "C:\apps\moscow2030-avatar-backend"
$appName = "moscow2030-avatar-backend"

function Show-Status {
    Write-Host "📊 Статус системы:" -ForegroundColor Cyan
    
    # PM2 статус
    Write-Host "`n🔧 PM2 процессы:" -ForegroundColor Yellow
    pm2 status
    
    # nginx статус
    Write-Host "`n🌐 nginx процессы:" -ForegroundColor Yellow
    Get-Process nginx -ErrorAction SilentlyContinue | Format-Table Name, Id, CPU, WorkingSet
    
    # Порты
    Write-Host "`n🔌 Открытые порты:" -ForegroundColor Yellow
    netstat -an | findstr ":80 :443 :3002"
    
    # Последние логи
    Write-Host "`n📝 Последние логи (5 строк):" -ForegroundColor Yellow
    if (Test-Path "$appPath\logs\combined.log") {
        Get-Content "$appPath\logs\combined.log" -Tail 5
    }
}

function Start-Application {
    Write-Host "🚀 Запуск Moscow 2030 Avatar Backend..." -ForegroundColor Green
    
    Set-Location $appPath
    
    # Запуск PM2
    Write-Host "▶️ Запуск Node.js приложения..." -ForegroundColor Yellow
    pm2 start ecosystem.config.js --env production
    pm2 save
    
    # Запуск nginx
    Write-Host "▶️ Запуск nginx..." -ForegroundColor Yellow
    try {
        if (Test-Path "C:\nginx\nginx.exe") {
            Start-Process -FilePath "C:\nginx\nginx.exe" -WorkingDirectory "C:\nginx" -WindowStyle Hidden
            Write-Host "✅ nginx запущен" -ForegroundColor Green
        } else {
            Write-Host "⚠️ nginx не найден в C:\nginx\" -ForegroundColor Yellow
        }
    } catch {
        Write-Host "❌ Ошибка запуска nginx: $($_.Exception.Message)" -ForegroundColor Red
    }
    
    Start-Sleep 3
    Show-Status
}

function Stop-Application {
    Write-Host "⏹️ Остановка Moscow 2030 Avatar Backend..." -ForegroundColor Red
    
    # Остановка PM2
    Write-Host "⏹️ Остановка Node.js приложения..." -ForegroundColor Yellow
    pm2 stop $appName
    
    # Остановка nginx
    Write-Host "⏹️ Остановка nginx..." -ForegroundColor Yellow
    try {
        if (Test-Path "C:\nginx\nginx.exe") {
            Start-Process -FilePath "C:\nginx\nginx.exe" -ArgumentList "-s", "stop" -WorkingDirectory "C:\nginx" -WindowStyle Hidden -Wait
            Write-Host "✅ nginx остановлен" -ForegroundColor Green
        }
    } catch {
        # Принудительная остановка
        Get-Process nginx -ErrorAction SilentlyContinue | Stop-Process -Force
        Write-Host "✅ nginx остановлен принудительно" -ForegroundColor Green
    }
    
    Write-Host "✅ Приложение остановлено" -ForegroundColor Green
}

function Restart-Application {
    Write-Host "🔄 Перезапуск Moscow 2030 Avatar Backend..." -ForegroundColor Cyan
    
    Stop-Application
    Start-Sleep 2
    Start-Application
}

function Show-Logs {
    Write-Host "📝 Логи Moscow 2030 Avatar Backend:" -ForegroundColor Cyan
    
    Write-Host "`n🔧 PM2 логи:" -ForegroundColor Yellow
    pm2 logs $appName --lines 20
    
    Write-Host "`n📋 Логи приложения:" -ForegroundColor Yellow
    if (Test-Path "$appPath\logs\combined.log") {
        Get-Content "$appPath\logs\combined.log" -Tail 20
    }
    
    Write-Host "`n🌐 nginx логи:" -ForegroundColor Yellow
    if (Test-Path "C:\nginx\logs\error.log") {
        Get-Content "C:\nginx\logs\error.log" -Tail 10
    }
}

function Update-Application {
    Write-Host "📥 Обновление Moscow 2030 Avatar Backend..." -ForegroundColor Cyan
    
    Set-Location $appPath
    
    # Остановка приложения
    Write-Host "⏹️ Остановка приложения..." -ForegroundColor Yellow
    pm2 stop $appName
    
    # Обновление кода
    Write-Host "📥 Получение обновлений..." -ForegroundColor Yellow
    git pull origin main
    
    # Установка зависимостей
    Write-Host "📦 Обновление зависимостей..." -ForegroundColor Yellow
    npm install --production
    
    # Перезапуск
    Write-Host "🔄 Перезапуск приложения..." -ForegroundColor Yellow
    pm2 reload $appName
    
    # Перезагрузка nginx
    if (Test-Path "C:\nginx\nginx.exe") {
        Write-Host "🔄 Перезагрузка nginx..." -ForegroundColor Yellow
        Start-Process -FilePath "C:\nginx\nginx.exe" -ArgumentList "-s", "reload" -WorkingDirectory "C:\nginx" -WindowStyle Hidden -Wait
    }
    
    Write-Host "✅ Обновление завершено!" -ForegroundColor Green
    Show-Status
}

# Выполнение действия
switch ($Action) {
    "start" { Start-Application }
    "stop" { Stop-Application }
    "restart" { Restart-Application }
    "status" { Show-Status }
    "logs" { Show-Logs }
    "update" { Update-Application }
}

Write-Host "`n💡 Использование:" -ForegroundColor Cyan
Write-Host "  .\manage.ps1 start    - Запустить приложение" -ForegroundColor White
Write-Host "  .\manage.ps1 stop     - Остановить приложение" -ForegroundColor White
Write-Host "  .\manage.ps1 restart  - Перезапустить приложение" -ForegroundColor White
Write-Host "  .\manage.ps1 status   - Показать статус" -ForegroundColor White
Write-Host "  .\manage.ps1 logs     - Показать логи" -ForegroundColor White
Write-Host "  .\manage.ps1 update   - Обновить приложение" -ForegroundColor White