const { exec } = require('child_process');
const { promisify } = require('util');
const path = require('path');
const logger = require('../utils/logger');

const execAsync = promisify(exec);

class PrinterService {
  constructor() {
    this.printerName = process.env.PRINTER_NAME || 'DNP_DS-RX1HS';
    this.printQueue = [];
    this.isProcessing = false;
  }

  /**
   * Печать изображения
   */
  async printImage(imagePath, options = {}) {
    const printJob = {
      id: this.generateJobId(),
      imagePath,
      options,
      status: 'queued',
      createdAt: new Date(),
      userId: options.userId
    };

    this.printQueue.push(printJob);
    logger.info(`Задание печати добавлено в очередь: ${printJob.id}`);

    // Запускаем обработку очереди
    this.processQueue();

    return printJob.id;
  }

  /**
   * Обработка очереди печати
   */
  async processQueue() {
    if (this.isProcessing || this.printQueue.length === 0) {
      return;
    }

    this.isProcessing = true;

    while (this.printQueue.length > 0) {
      const job = this.printQueue.shift();
      
      try {
        job.status = 'printing';
        logger.info(`Начинаем печать задания: ${job.id}`);

        await this.sendToPrinter(job.imagePath, job.options);
        
        job.status = 'completed';
        job.completedAt = new Date();
        
        logger.info(`Задание печати завершено: ${job.id}`);
        
      } catch (error) {
        job.status = 'failed';
        job.error = error.message;
        job.failedAt = new Date();
        
        logger.error(`Ошибка печати задания ${job.id}:`, error);
      }
    }

    this.isProcessing = false;
  }

  /**
   * Отправка файла на принтер
   */
  async sendToPrinter(imagePath, options) {
    const { format = '4x6', copies = 1 } = options;

    logger.info(`Начинаем отправку на принтер: ${imagePath}, формат: ${format}, копий: ${copies}`);
    logger.info(`Платформа: ${process.platform}, принтер: ${this.printerName}`);

    try {
      let command;
      const isHtmlFile = imagePath.toLowerCase().endsWith('.html');
      
      if (process.platform === 'darwin') {
        // macOS
        if (isHtmlFile) {
          command = `open -a "Google Chrome" "${imagePath}" && sleep 2 && osascript -e 'tell application "Google Chrome" to print front document'`;
        } else {
          command = `lpr -P "${this.printerName}" -o media=${format} -o copies=${copies} "${imagePath}"`;
        }
      } else if (process.platform === 'win32') {
        // Windows
        if (isHtmlFile) {
          // Для HTML файлов используем браузер
          command = `powershell -Command "Start-Process -FilePath '${imagePath}' -Verb Print -WindowStyle Hidden"`;
        } else {
          // Для изображений используем обычную печать
          command = `powershell -Command "Start-Process -FilePath '${imagePath}' -Verb Print -WindowStyle Hidden"`;
        }
      } else {
        // Linux
        if (isHtmlFile) {
          command = `firefox "${imagePath}" -print`;
        } else {
          command = `lp -d "${this.printerName}" -o media=${format} -n ${copies} "${imagePath}"`;
        }
      }

      logger.info(`Выполняем команду печати (${isHtmlFile ? 'HTML' : 'IMAGE'}): ${command}`);
      
      const { stdout, stderr } = await execAsync(command, { timeout: 15000 });
      
      logger.info(`Stdout: ${stdout || 'Нет вывода'}`);
      if (stderr) logger.info(`Stderr: ${stderr}`);
      
      if (stderr && !stderr.includes('Warning') && !stderr.includes('EnableVisualStyles') && !stderr.includes('Chrome')) {
        logger.warn(`Предупреждение при печати: ${stderr}`);
      }

      logger.info(`Файл отправлен на печать: ${imagePath}`);
      return stdout || 'Print command executed successfully';

    } catch (error) {
      logger.error('Ошибка отправки на принтер:', error);
      
      // Fallback для HTML файлов - попробуем альтернативный способ
      if (imagePath.toLowerCase().endsWith('.html') && process.platform === 'win32') {
        try {
          logger.info('Пробуем альтернативный способ печати HTML...');
          const fallbackCommand = `powershell -Command "& { $ie = New-Object -ComObject InternetExplorer.Application; $ie.Navigate('file:///${imagePath.replace(/\\/g, '/')}'); Start-Sleep -Seconds 3; $ie.ExecWB(6, 2); $ie.Quit() }"`;
          
          const { stdout: fbStdout, stderr: fbStderr } = await execAsync(fallbackCommand, { timeout: 10000 });
          logger.info(`Fallback успешен: ${fbStdout || 'OK'}`);
          return fbStdout || 'Fallback print executed';
          
        } catch (fallbackError) {
          logger.error('Fallback также не сработал:', fallbackError);
        }
      }
      
      throw new Error(`Не удалось отправить на печать: ${error.message}`);
    }
  }

  /**
   * Получить статус принтера
   */
  async getStatus() {
    try {
      let command;
      
      if (process.platform === 'darwin') {
        command = `lpstat -p "${this.printerName}"`;
      } else if (process.platform === 'win32') {
        command = `powershell -Command "Get-Printer -Name '${this.printerName}' | Select-Object Name, PrinterStatus, JobCount"`;
      } else {
        command = `lpstat -p "${this.printerName}"`;
      }

      const { stdout } = await execAsync(command);
      
      return {
        printerName: this.printerName,
        status: this.parsePrinterStatus(stdout),
        queueLength: this.printQueue.length,
        isProcessing: this.isProcessing,
        lastChecked: new Date()
      };

    } catch (error) {
      logger.error('Ошибка получения статуса принтера:', error);
      return {
        printerName: this.printerName,
        status: 'unknown',
        error: error.message,
        queueLength: this.printQueue.length,
        isProcessing: this.isProcessing,
        lastChecked: new Date()
      };
    }
  }

  /**
   * Парсинг статуса принтера
   */
  parsePrinterStatus(output) {
    if (output.includes('idle')) return 'ready';
    if (output.includes('printing')) return 'printing';
    if (output.includes('stopped')) return 'stopped';
    if (output.includes('disabled')) return 'disabled';
    return 'unknown';
  }

  /**
   * Получить очередь печати
   */
  async getQueue() {
    return {
      queue: this.printQueue.map(job => ({
        id: job.id,
        status: job.status,
        format: job.options.format,
        copies: job.options.copies,
        userId: job.userId,
        createdAt: job.createdAt,
        completedAt: job.completedAt,
        failedAt: job.failedAt,
        error: job.error
      })),
      isProcessing: this.isProcessing,
      totalJobs: this.printQueue.length
    };
  }

  /**
   * Генерация ID задания
   */
  generateJobId() {
    return `print_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Получить список доступных принтеров
   */
  async getAvailablePrinters() {
    try {
      let command;
      
      if (process.platform === 'darwin') {
        // macOS
        command = 'lpstat -p';
      } else if (process.platform === 'win32') {
        // Windows
        command = 'powershell -Command "Get-Printer | Select-Object Name, PrinterStatus, DriverName | ConvertTo-Json"';
      } else {
        // Linux
        command = 'lpstat -p';
      }

      const { stdout } = await execAsync(command);
      
      return {
        platform: process.platform,
        defaultPrinter: this.printerName,
        availablePrinters: this.parsePrinterList(stdout),
        rawOutput: stdout
      };

    } catch (error) {
      logger.error('Ошибка получения списка принтеров:', error);
      return {
        platform: process.platform,
        defaultPrinter: this.printerName,
        availablePrinters: [],
        error: error.message,
        rawOutput: ''
      };
    }
  }

  /**
   * Парсинг списка принтеров
   */
  parsePrinterList(output) {
    const printers = [];
    
    if (process.platform === 'win32') {
      try {
        // Windows PowerShell JSON output
        const parsed = JSON.parse(output);
        const printerArray = Array.isArray(parsed) ? parsed : [parsed];
        
        return printerArray.map(printer => ({
          name: printer.Name,
          status: this.mapWindowsStatus(printer.PrinterStatus),
          driver: printer.DriverName,
          isDefault: printer.Name === this.printerName
        }));
      } catch (e) {
        // Fallback для Windows
        return [{ 
          name: this.printerName, 
          status: 'unknown', 
          driver: 'Unknown',
          isDefault: true 
        }];
      }
    } else {
      // macOS/Linux lpstat output
      const lines = output.split('\n').filter(line => line.trim());
      
      for (const line of lines) {
        if (line.startsWith('printer ')) {
          const match = line.match(/printer (\S+) (.+)/);
          if (match) {
            printers.push({
              name: match[1],
              status: match[2].includes('idle') ? 'ready' : 
                     match[2].includes('printing') ? 'printing' : 'unknown',
              driver: 'Unknown',
              isDefault: match[1] === this.printerName
            });
          }
        }
      }
    }
    
    return printers;
  }

  /**
   * Маппинг статусов Windows принтера
   */
  mapWindowsStatus(status) {
    const statusMap = {
      1: 'other',
      2: 'unknown', 
      3: 'ready',
      4: 'printing',
      5: 'warmup',
      6: 'stopped',
      7: 'offline'
    };
    
    return statusMap[status] || 'unknown';
  }

  /**
   * Очистка завершенных заданий (вызывать периодически)
   */
  cleanupCompletedJobs() {
    const cutoffTime = new Date(Date.now() - 24 * 60 * 60 * 1000); // 24 часа назад
    
    this.printQueue = this.printQueue.filter(job => {
      return !(job.status === 'completed' && job.completedAt < cutoffTime);
    });
  }
}

module.exports = new PrinterService();