# 🚀 Деплой Moscow 2030 Avatar Backend на Windows

Пошаговая инструкция по развертыванию системы печати на Windows сервере с nginx.

## 📋 Требования

- **Windows Server 2019/2022** или **Windows 10/11**
- **Node.js 18+** 
- **nginx для Windows**
- **PM2** (менеджер процессов)
- **Принтер DNP DS-RX1HS** (или любой Windows принтер)

## 🛠️ Установка зависимостей

### 1. Установка Node.js

```powershell
# Скачай и установи Node.js с официального сайта
# https://nodejs.org/en/download/

# Проверь установку
node --version
npm --version
```

### 2. Установка PM2 (менедж<PERSON><PERSON> процессов)

```powershell
# Установи PM2 глобально
npm install -g pm2

# Установи PM2 как Windows Service
npm install -g pm2-windows-service
pm2-service-install
```

### 3. Установка nginx для Windows

```powershell
# Скачай nginx для Windows с официального сайта
# http://nginx.org/en/download.html

# Распакуй в C:\nginx
# Или используй Chocolatey:
choco install nginx
```

## 📁 Подготовка проекта

### 1. Клонирование и настройка

```powershell
# Создай папку для проекта
mkdir C:\apps\moscow2030-avatar-backend
cd C:\apps\moscow2030-avatar-backend

# Клонируй репозиторий
git clone https://github.com/Den999/moscow2030-avatar-backend.git .

# Установи зависимости
npm install --production

# Создай production .env файл
copy .env.example .env.production
```

### 2. Настройка .env.production

```env
# Production настройки
NODE_ENV=production
PORT=3002

# Принтер (замени на свой)
PRINTER_NAME=DNP_DS-RX1HS

# Логирование
LOG_LEVEL=info

# Безопасность
MAX_FILE_SIZE=10485760
DOWNLOAD_TIMEOUT=30000

# Папки
TEMP_DIR=C:\apps\moscow2030-avatar-backend\temp
LOGS_DIR=C:\apps\moscow2030-avatar-backend\logs
```

### 3. Создание необходимых папок

```powershell
# Создай папки для логов и временных файлов
mkdir logs
mkdir temp

# Установи права доступа
icacls logs /grant "Everyone:(OI)(CI)F"
icacls temp /grant "Everyone:(OI)(CI)F"
```

## 🔧 Настройка PM2

### 1. Создание PM2 конфигурации

Создай файл `ecosystem.config.js`:

```javascript
module.exports = {
  apps: [{
    name: 'moscow2030-avatar-backend',
    script: 'server.js',
    cwd: 'C:\\apps\\moscow2030-avatar-backend',
    env_production: {
      NODE_ENV: 'production',
      PORT: 3002
    },
    instances: 1,
    exec_mode: 'fork',
    watch: false,
    max_memory_restart: '500M',
    error_file: 'logs/pm2-error.log',
    out_file: 'logs/pm2-out.log',
    log_file: 'logs/pm2-combined.log',
    time: true,
    autorestart: true,
    max_restarts: 10,
    min_uptime: '10s',
    restart_delay: 5000
  }]
};
```

### 2. Запуск через PM2

```powershell
# Запусти приложение
pm2 start ecosystem.config.js --env production

# Проверь статус
pm2 status

# Посмотри логи
pm2 logs moscow2030-avatar-backend

# Сохрани конфигурацию для автозапуска
pm2 save
pm2 startup
```

## 🌐 Настройка nginx

### 1. Конфигурация nginx

Создай файл `C:\nginx\conf\sites\moscow2030-avatar.conf`:

```nginx
# Upstream для Node.js приложения
upstream moscow2030_backend {
    server 127.0.0.1:3002;
    keepalive 32;
}

# HTTP сервер (редирект на HTTPS)
server {
    listen 80;
    server_name your-domain.com www.your-domain.com;
    
    # Редирект на HTTPS
    return 301 https://$server_name$request_uri;
}

# HTTPS сервер
server {
    listen 443 ssl http2;
    server_name your-domain.com www.your-domain.com;
    
    # SSL сертификаты (замени на свои)
    ssl_certificate C:/nginx/ssl/your-domain.crt;
    ssl_certificate_key C:/nginx/ssl/your-domain.key;
    
    # SSL настройки
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    
    # Безопасность
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    
    # Логи
    access_log C:/nginx/logs/moscow2030-access.log;
    error_log C:/nginx/logs/moscow2030-error.log;
    
    # Основная локация - проксирование к Node.js
    location / {
        proxy_pass http://moscow2030_backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }
    
    # API эндпоинты с увеличенным таймаутом
    location /api/ {
        proxy_pass http://moscow2030_backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        client_max_body_size 50M;
    }
    
    # Статические файлы (если есть)
    location /static/ {
        alias C:/apps/moscow2030-avatar-backend/public/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # Health check
    location /health {
        proxy_pass http://moscow2030_backend;
        access_log off;
    }
    
    # Блокировка доступа к служебным файлам
    location ~ /\. {
        deny all;
    }
    
    location ~ \.(env|log)$ {
        deny all;
    }
}
```

### 2. Основная конфигурация nginx

Обнови `C:\nginx\conf\nginx.conf`:

```nginx
worker_processes auto;
error_log logs/error.log;
pid logs/nginx.pid;

events {
    worker_connections 1024;
    use select;
}

http {
    include mime.types;
    default_type application/octet-stream;
    
    # Логирование
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';
    
    access_log logs/access.log main;
    
    # Основные настройки
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    client_max_body_size 50M;
    
    # Gzip сжатие
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/javascript
        application/xml+rss
        application/json;
    
    # Включаем конфигурации сайтов
    include sites/*.conf;
}
```

### 3. Запуск nginx

```powershell
# Перейди в папку nginx
cd C:\nginx

# Проверь конфигурацию
nginx -t

# Запусти nginx
nginx

# Или перезапусти если уже запущен
nginx -s reload

# Остановить nginx
nginx -s stop
```

## 🔒 SSL сертификат (опционально)

### Вариант 1: Let's Encrypt (бесплатный)

```powershell
# Установи Certbot для Windows
# https://certbot.eff.org/instructions?ws=nginx&os=windows

# Получи сертификат
certbot --nginx -d your-domain.com -d www.your-domain.com
```

### Вариант 2: Самоподписанный сертификат (для тестирования)

```powershell
# Создай папку для SSL
mkdir C:\nginx\ssl

# Создай самоподписанный сертификат
openssl req -x509 -nodes -days 365 -newkey rsa:2048 -keyout C:\nginx\ssl\moscow2030.key -out C:\nginx\ssl\moscow2030.crt
```

## 🔥 Windows Service (автозапуск)

### 1. Создание службы для nginx

Создай файл `install-nginx-service.bat`:

```batch
@echo off
sc create "nginx" binPath= "C:\nginx\nginx.exe" DisplayName= "nginx HTTP Server" start= auto
sc description "nginx" "nginx HTTP Server for Moscow 2030 Avatar Backend"
sc start "nginx"
pause
```

### 2. Создание службы для Node.js (через PM2)

```powershell
# PM2 уже настроен как служба, но можно проверить
pm2-service-install

# Проверь что служба запущена
Get-Service PM2*
```

## 📊 Мониторинг и логи

### 1. Просмотр логов

```powershell
# Логи PM2
pm2 logs moscow2030-avatar-backend

# Логи nginx
Get-Content C:\nginx\logs\moscow2030-access.log -Tail 50 -Wait

# Логи приложения
Get-Content C:\apps\moscow2030-avatar-backend\logs\combined.log -Tail 50 -Wait
```

### 2. Мониторинг PM2

```powershell
# Статус процессов
pm2 status

# Мониторинг в реальном времени
pm2 monit

# Перезапуск приложения
pm2 restart moscow2030-avatar-backend

# Обновление приложения
pm2 reload moscow2030-avatar-backend
```

## 🔧 Обновление приложения

### 1. Скрипт обновления

Создай файл `update.bat`:

```batch
@echo off
echo Updating Moscow 2030 Avatar Backend...

cd C:\apps\moscow2030-avatar-backend

echo Pulling latest changes...
git pull origin main

echo Installing dependencies...
npm install --production

echo Restarting application...
pm2 reload moscow2030-avatar-backend

echo Reloading nginx...
nginx -s reload

echo Update completed!
pause
```

### 2. Автоматическое обновление (опционально)

```powershell
# Создай задачу в планировщике Windows для автоматического обновления
schtasks /create /tn "Moscow2030 Update" /tr "C:\apps\moscow2030-avatar-backend\update.bat" /sc daily /st 03:00
```

## 🚨 Безопасность

### 1. Firewall настройки

```powershell
# Открой порты для HTTP/HTTPS
New-NetFirewallRule -DisplayName "HTTP" -Direction Inbound -Protocol TCP -LocalPort 80 -Action Allow
New-NetFirewallRule -DisplayName "HTTPS" -Direction Inbound -Protocol TCP -LocalPort 443 -Action Allow

# Закрой прямой доступ к Node.js порту (3002)
New-NetFirewallRule -DisplayName "Block Node.js Direct" -Direction Inbound -Protocol TCP -LocalPort 3002 -Action Block
```

### 2. Ограничение доступа к API

Добавь в nginx конфигурацию:

```nginx
# Ограничение по IP для административных функций
location /api/print/queue-settings {
    allow ***********/24;  # Замени на свою сеть
    allow 127.0.0.1;
    deny all;
    proxy_pass http://moscow2030_backend;
}

# Rate limiting для API
limit_req_zone $binary_remote_addr zone=api:10m rate=10r/m;

location /api/ {
    limit_req zone=api burst=20 nodelay;
    proxy_pass http://moscow2030_backend;
}
```

## 📋 Чек-лист деплоя

### ✅ Перед запуском:
- [ ] Node.js установлен
- [ ] PM2 установлен и настроен как служба
- [ ] nginx установлен
- [ ] Проект склонирован в `C:\apps\moscow2030-avatar-backend`
- [ ] `.env.production` настроен
- [ ] Папки `logs` и `temp` созданы
- [ ] Принтер подключен и настроен

### ✅ После запуска:
- [ ] PM2 показывает статус "online"
- [ ] nginx запущен без ошибок
- [ ] `http://localhost` открывается
- [ ] API эндпоинты отвечают
- [ ] Логи пишутся корректно
- [ ] Принтер печатает тестовые задания

## 🧪 Тестирование деплоя

```powershell
# Проверь health check
curl http://localhost/health

# Проверь API
curl -X POST http://localhost/api/print/simple -H "Content-Type: application/json" -d '{\"text\": \"Production Test\"}'

# Проверь статус очереди
curl http://localhost/api/print/queue-status
```

## 🆘 Troubleshooting

### Проблема: PM2 не запускается
```powershell
# Переустанови PM2 service
pm2-service-uninstall
pm2-service-install
```

### Проблема: nginx не стартует
```powershell
# Проверь конфигурацию
nginx -t

# Проверь логи
Get-Content C:\nginx\logs\error.log
```

### Проблема: Принтер не печатает
```powershell
# Проверь список принтеров
Get-Printer | Select-Object Name, PrinterStatus

# Обнови PRINTER_NAME в .env.production
```

## 🎯 Production готов!

После выполнения всех шагов у тебя будет:

1. ✅ **Автозапуск** - PM2 и nginx запускаются с Windows
2. ✅ **Мониторинг** - логи и статистика
3. ✅ **Безопасность** - HTTPS и ограничения доступа
4. ✅ **Масштабируемость** - nginx как reverse proxy
5. ✅ **Надежность** - автоматический перезапуск при сбоях

**Твоя система готова к production нагрузкам!** 🚀