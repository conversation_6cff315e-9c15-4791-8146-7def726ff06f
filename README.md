# 🖨️ Moscow 2030 Avatar Backend

Backend система для печати аватаров и изображений с поддержкой множества источников и форматов.

## 🚀 Быстрый старт

### Установка

```bash
# Клонирование репозитория
git clone https://github.com/Den999/moscow2030-avatar-backend.git
cd moscow2030-avatar-backend

# Установка зависимостей
npm install

# Создание .env файла
cp .env.example .env

# Запуск сервера
npm run dev
```

Сервер запустится на `http://localhost:3002`

### Веб-интерфейс

Откройте `http://localhost:3002` в браузере для доступа к веб-интерфейсу тестирования.

## 📡 API Endpoints

### 1. 🖼️ Универсальная печать изображений

**Endpoint:** `POST /api/print/imgbb`

**Описание:** Печать изображений с любых источников (imgbb, прямые ссылки, CDN)

**Параметры:**
- `imageUrl` (string, обязательный) - URL изображения
- `format` (string, опциональный) - Формат печати: `4x6`, `6x8`, `5x7`, `2x6` (по умолчанию: `4x6`)
- `copies` (number, опциональный) - Количество копий: 1-5 (по умолчанию: 1)
- `userId` (string, опциональный) - ID пользователя

**Поддерживаемые источники:**
- ✅ **imgbb.com** - страницы просмотра и прямые ссылки
- ✅ **Любые прямые ссылки** на изображения
- ✅ **CDN сервисы**

**Поддерживаемые форматы:** JPG, PNG, GIF, BMP, WEBP, TIFF, SVG

#### Примеры curl запросов:

```bash
# Печать с imgbb (страница просмотра)
curl -X POST http://localhost:3002/api/print/imgbb \
  -H "Content-Type: application/json" \
  -d '{
    "imageUrl": "https://ibb.co/27dDbm7d",
    "format": "4x6",
    "copies": 1,
    "userId": "user123"
  }'

# Печать с imgbb (прямая ссылка)
curl -X POST http://localhost:3002/api/print/imgbb \
  -H "Content-Type: application/json" \
  -d '{
    "imageUrl": "https://i.ibb.co/QFM25zFM/pexels-enricoperini-819767.jpg",
    "format": "6x8",
    "copies": 2,
    "userId": "user456"
  }'

# Печать тестового изображения
curl -X POST http://localhost:3002/api/print/imgbb \
  -H "Content-Type: application/json" \
  -d '{
    "imageUrl": "https://via.placeholder.com/600x400/FF0000/FFFFFF?text=TEST+IMAGE",
    "format": "4x6",
    "copies": 1,
    "userId": "test_user"
  }'

# Печать случайного фото
curl -X POST http://localhost:3002/api/print/imgbb \
  -H "Content-Type: application/json" \
  -d '{
    "imageUrl": "https://picsum.photos/600/400",
    "format": "5x7",
    "copies": 1,
    "userId": "random_user"
  }'
```

**Успешный ответ:**
```json
{
  "success": true,
  "message": "Изображение отправлено на печать",
  "printJobId": "print_1753267390092_abc123",
  "format": "4x6",
  "copies": 1,
  "estimatedTime": 13,
  "tempFilePath": "C:\\...\\temp\\print_imgbb_user123_1753267390092.jpg"
}
```

### 2. 📝 Простая текстовая печать

**Endpoint:** `POST /api/print/simple`

**Описание:** Печать простого текста (самый надежный способ)

```bash
curl -X POST http://localhost:3002/api/print/simple \
  -H "Content-Type: application/json" \
  -d '{
    "text": "ТЕСТОВАЯ ПЕЧАТЬ",
    "copies": 1,
    "userId": "text_user"
  }'
```

### 3. 🧪 HTML тестовая печать

**Endpoint:** `POST /api/print/test`

**Описание:** Печать HTML тестового изображения

```bash
curl -X POST http://localhost:3002/api/print/test \
  -H "Content-Type: application/json" \
  -d '{
    "format": "4x6",
    "copies": 1,
    "userId": "test_user"
  }'
```

### 4. 📁 Прямая печать файла

**Endpoint:** `POST /api/print/direct`

**Описание:** Печать файла напрямую с диска

```bash
curl -X POST http://localhost:3002/api/print/direct \
  -H "Content-Type: application/json" \
  -d '{
    "filePath": "C:\\path\\to\\image.jpg",
    "format": "4x6",
    "copies": 1,
    "userId": "direct_user"
  }'
```

### 5. ⚡ Выполнение системных команд

**Endpoint:** `POST /api/print/command`

**Описание:** Выполнение системных команд Windows

```bash
# Список принтеров
curl -X POST http://localhost:3002/api/print/command \
  -H "Content-Type: application/json" \
  -d '{
    "command": "powershell -Command \"Get-Printer | Select-Object Name, PrinterStatus\"",
    "description": "Список принтеров"
  }'

# Задания печати
curl -X POST http://localhost:3002/api/print/command \
  -H "Content-Type: application/json" \
  -d '{
    "command": "powershell -Command \"Get-PrintJob\"",
    "description": "Задания печати"
  }'
```

### 6. 📊 Статус и мониторинг

```bash
# Проверка здоровья сервера
curl http://localhost:3002/health

# Статус принтера
curl http://localhost:3002/api/print/status

# Список доступных принтеров
curl http://localhost:3002/api/print/printers

# Очередь печати
curl http://localhost:3002/api/print/queue
```

## 🔧 Настройка

### Переменные окружения (.env)

```env
# Порт сервера
PORT=3002

# Настройки принтера
PRINTER_NAME=DNP_DS-RX1HS

# Уровень логирования
LOG_LEVEL=info

# Режим работы
NODE_ENV=development

# Максимальный размер файла (байты)
MAX_FILE_SIZE=10485760

# Таймаут скачивания (мс)
DOWNLOAD_TIMEOUT=30000
```

### Настройка принтера

#### Windows:
```powershell
# Список доступных принтеров
Get-Printer | Select-Object Name, PrinterStatus

# Обновите PRINTER_NAME в .env файле
PRINTER_NAME=Точное_Имя_Принтера
```

#### Для тестирования без реального принтера:
```env
PRINTER_NAME=Microsoft Print to PDF
```

## 🎯 Примеры использования

### Быстрый тест печати

```bash
# 1. Простая текстовая печать (всегда работает)
curl -X POST http://localhost:3002/api/print/simple \
  -H "Content-Type: application/json" \
  -d '{"text": "HELLO WORLD", "copies": 1}'

# 2. Тестовое изображение
curl -X POST http://localhost:3002/api/print/imgbb \
  -H "Content-Type: application/json" \
  -d '{"imageUrl": "https://via.placeholder.com/600x400/FF0000/FFFFFF?text=TEST"}'

# 3. Печать с imgbb
curl -X POST http://localhost:3002/api/print/imgbb \
  -H "Content-Type: application/json" \
  -d '{"imageUrl": "https://ibb.co/YOUR_IMAGE_ID", "format": "4x6"}'
```

### Пакетная печать

```bash
# Печать нескольких копий
curl -X POST http://localhost:3002/api/print/imgbb \
  -H "Content-Type: application/json" \
  -d '{
    "imageUrl": "https://i.ibb.co/example/image.jpg",
    "format": "4x6",
    "copies": 5,
    "userId": "batch_user"
  }'
```

## 🚨 Обработка ошибок

### Типичные ошибки и решения:

**404 - Изображение не найдено:**
```json
{
  "success": false,
  "error": "Изображение не найдено (404). Проверьте URL."
}
```

**Принтер не найден:**
```json
{
  "success": false,
  "error": "Не удалось получить статус принтера"
}
```

**Неверный URL:**
```json
{
  "success": false,
  "error": "URL должен начинаться с http:// или https://"
}
```

## 📝 Логирование

Логи сохраняются в папке `logs/`:
- `combined.log` - все логи
- `error.log` - только ошибки

```bash
# Просмотр логов в реальном времени
tail -f logs/combined.log
```

## 🔍 Отладка

### Проверка системы:

```bash
# 1. Проверка сервера
curl http://localhost:3002/health

# 2. Список принтеров
curl -X POST http://localhost:3002/api/print/command \
  -H "Content-Type: application/json" \
  -d '{"command": "powershell -Command \"Get-Printer\""}'

# 3. Тест простой печати
curl -X POST http://localhost:3002/api/print/simple \
  -H "Content-Type: application/json" \
  -d '{"text": "DEBUG TEST"}'
```

## 🏗️ Архитектура

```
moscow2030-avatar-backend/
├── controllers/          # Контроллеры API
│   └── printController.js
├── services/             # Бизнес-логика
│   └── printerService.js
├── middleware/           # Промежуточное ПО
│   ├── errorHandler.js
│   └── validatePrintRequest.js
├── routes/               # Маршруты API
│   └── print.js
├── utils/                # Утилиты
│   └── logger.js
├── public/               # Веб-интерфейс
│   ├── index.html
│   └── app.js
├── temp/                 # Временные файлы
└── logs/                 # Логи
```

## 🤝 Поддерживаемые принтеры

- **DNP DS-RX1HS** (основной)
- **Microsoft Print to PDF** (для тестирования)
- **Любые Windows принтеры** (через стандартные команды)

## 🚀 Деплой в продакшн

Для развертывания системы на Windows сервере с nginx смотри подробную инструкцию:

**📖 [DEPLOYMENT.md](DEPLOYMENT.md)** - Полная инструкция по деплою

### Быстрый деплой:

```powershell
# 1. Запусти скрипт установки (от имени администратора)
.\scripts\install.ps1

# 2. Настрой .env.production файл
# 3. Установи nginx
# 4. Запусти приложение
.\scripts\manage.ps1 start
```

### Управление в продакшне:

```powershell
# Статус системы
.\scripts\manage.ps1 status

# Перезапуск
.\scripts\manage.ps1 restart

# Просмотр логов
.\scripts\manage.ps1 logs

# Обновление
.\scripts\manage.ps1 update
```

## 📄 Лицензия

MIT License - Moscow 2030 Avatar Backend
