# Тестовые запросы для Avatar Backend
# Используй в VS Code с расширением REST Client или в Postman

### Health Check
GET http://localhost:3002/health

### Статус принтера
GET http://localhost:3002/api/print/status

### Очередь печати
GET http://localhost:3002/api/print/queue

### Печать изображения с imgbb (замени URL на реальный)
POST http://localhost:3002/api/print/imgbb
Content-Type: application/json

{
  "imageUrl": "https://i.ibb.co/EXAMPLE/avatar.jpg",
  "format": "4x6",
  "copies": 1,
  "userId": "user123"
}

### Печать в формате 6x8
POST http://localhost:3002/api/print/imgbb
Content-Type: application/json

{
  "imageUrl": "https://i.ibb.co/EXAMPLE/avatar.jpg",
  "format": "6x8",
  "copies": 2,
  "userId": "user456"
}