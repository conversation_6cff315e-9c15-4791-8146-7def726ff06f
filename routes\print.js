const express = require('express');
const router = express.Router();
const printController = require('../controllers/printController');
const validatePrintRequest = require('../middleware/validatePrintRequest');

// POST /api/print/imgbb - печать изображения с imgbb
router.post('/imgbb', validatePrintRequest, printController.printFromImgbb);

// GET /api/print/status - статус принтера
router.get('/status', printController.getPrinterStatus);

// GET /api/print/queue - очередь печати
router.get('/queue', printController.getPrintQueue);

// GET /api/print/printers - список принтеров
router.get('/printers', printController.getAvailablePrinters);

// POST /api/print/test - тестовая печать черно-белого прямоугольника
router.post('/test', printController.printTestImage);

// POST /api/print/direct - прямая печать файла
router.post('/direct', printController.printDirectFile);

// POST /api/print/command - печать через системную команду
router.post('/command', printController.printSystemCommand);

// POST /api/print/simple - простая текстовая печать
router.post('/simple', printController.printSimpleText);

// GET /api/print/queue-status - статус очереди печати
router.get('/queue-status', printController.getQueueStatus);

// POST /api/print/queue-settings - настройки очереди
router.post('/queue-settings', printController.updateQueueSettings);

// POST /api/print/queue-cleanup - очистка истории очереди
router.post('/queue-cleanup', printController.cleanupQueue);

// GET /api/print/job/:jobId - получить информацию о задании
router.get('/job/:jobId', printController.getJobInfo);

// DELETE /api/print/job/:jobId - отменить задание
router.delete('/job/:jobId', printController.cancelJob);

module.exports = router;