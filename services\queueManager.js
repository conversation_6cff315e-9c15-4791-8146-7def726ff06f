const logger = require('../utils/logger');

class QueueManager {
  constructor() {
    this.queue = [];
    this.processing = [];
    this.completed = [];
    this.failed = [];
    this.maxConcurrent = 2; // Максимум 2 задания одновременно
    this.isProcessing = false;
    this.stats = {
      totalJobs: 0,
      completedJobs: 0,
      failedJobs: 0,
      startTime: new Date()
    };
  }

  /**
   * Добавить задание в очередь
   */
  addJob(jobData) {
    const job = {
      id: this.generateJobId(),
      ...jobData,
      status: 'queued',
      createdAt: new Date(),
      priority: jobData.priority || 1, // 1 = низкий, 2 = средний, 3 = высокий
      retries: 0,
      maxRetries: 3
    };

    // Добавляем в очередь с учетом приоритета
    this.queue.push(job);
    this.queue.sort((a, b) => b.priority - a.priority); // Сортируем по приоритету

    this.stats.totalJobs++;
    
    logger.info(`Задание добавлено в очередь: ${job.id}, приоритет: ${job.priority}, позиция в очереди: ${this.queue.length}`);
    
    // Запускаем обработку очереди
    this.processQueue();
    
    return job.id;
  }

  /**
   * Обработка очереди
   */
  async processQueue() {
    if (this.isProcessing) {
      return;
    }

    this.isProcessing = true;

    while (this.queue.length > 0 && this.processing.length < this.maxConcurrent) {
      const job = this.queue.shift();
      this.processing.push(job);
      
      logger.info(`Начинаем обработку задания: ${job.id} (${this.processing.length}/${this.maxConcurrent} слотов занято)`);
      
      // Обрабатываем задание асинхронно
      this.processJob(job).catch(error => {
        logger.error(`Критическая ошибка при обработке задания ${job.id}:`, error);
      });
    }

    this.isProcessing = false;
  }

  /**
   * Обработка одного задания
   */
  async processJob(job) {
    try {
      job.status = 'processing';
      job.startedAt = new Date();
      
      logger.info(`Обрабатываем задание: ${job.id}, тип: ${job.type}`);

      // Выполняем задание в зависимости от типа
      let result;
      switch (job.type) {
        case 'image':
          result = await this.processImageJob(job);
          break;
        case 'text':
          result = await this.processTextJob(job);
          break;
        case 'html':
          result = await this.processHtmlJob(job);
          break;
        case 'direct':
          result = await this.processDirectJob(job);
          break;
        default:
          throw new Error(`Неизвестный тип задания: ${job.type}`);
      }

      // Успешное завершение
      job.status = 'completed';
      job.completedAt = new Date();
      job.result = result;
      
      this.moveJobToCompleted(job);
      this.stats.completedJobs++;
      
      logger.info(`Задание успешно завершено: ${job.id}, время выполнения: ${job.completedAt - job.startedAt}ms`);

    } catch (error) {
      logger.error(`Ошибка при обработке задания ${job.id}:`, error);
      
      job.retries++;
      job.lastError = error.message;
      
      if (job.retries < job.maxRetries) {
        // Повторная попытка
        logger.info(`Повторная попытка для задания ${job.id} (${job.retries}/${job.maxRetries})`);
        job.status = 'retrying';
        
        // Добавляем обратно в очередь с задержкой
        setTimeout(() => {
          this.queue.unshift(job); // Добавляем в начало очереди
          this.processQueue();
        }, 5000 * job.retries); // Увеличиваем задержку с каждой попыткой
        
      } else {
        // Максимум попыток исчерпан
        job.status = 'failed';
        job.failedAt = new Date();
        
        this.moveJobToFailed(job);
        this.stats.failedJobs++;
        
        logger.error(`Задание окончательно провалено: ${job.id} после ${job.retries} попыток`);
      }
    } finally {
      // Убираем из списка обрабатываемых
      this.processing = this.processing.filter(j => j.id !== job.id);
      
      // Продолжаем обработку очереди
      setTimeout(() => this.processQueue(), 100);
    }
  }

  /**
   * Обработка задания печати изображения
   */
  async processImageJob(job) {
    const printerService = require('./printerService');
    
    // Имитируем обработку изображения
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    return await printerService.printImage(job.filePath, {
      format: job.format,
      copies: job.copies,
      userId: job.userId
    });
  }

  /**
   * Обработка задания печати текста
   */
  async processTextJob(job) {
    const printerService = require('./printerService');
    
    return await printerService.printImage(job.filePath, {
      format: 'text',
      copies: job.copies,
      userId: job.userId
    });
  }

  /**
   * Обработка задания печати HTML
   */
  async processHtmlJob(job) {
    const printerService = require('./printerService');
    
    return await printerService.printImage(job.filePath, {
      format: job.format,
      copies: job.copies,
      userId: job.userId
    });
  }

  /**
   * Обработка прямой печати файла
   */
  async processDirectJob(job) {
    const printerService = require('./printerService');
    
    return await printerService.printImage(job.filePath, {
      format: job.format,
      copies: job.copies,
      userId: job.userId
    });
  }

  /**
   * Переместить задание в завершенные
   */
  moveJobToCompleted(job) {
    this.completed.push(job);
    
    // Ограничиваем размер истории
    if (this.completed.length > 100) {
      this.completed = this.completed.slice(-50);
    }
  }

  /**
   * Переместить задание в провалившиеся
   */
  moveJobToFailed(job) {
    this.failed.push(job);
    
    // Ограничиваем размер истории
    if (this.failed.length > 50) {
      this.failed = this.failed.slice(-25);
    }
  }

  /**
   * Получить статус очереди
   */
  getQueueStatus() {
    return {
      queue: {
        waiting: this.queue.length,
        processing: this.processing.length,
        completed: this.completed.length,
        failed: this.failed.length
      },
      stats: {
        ...this.stats,
        uptime: new Date() - this.stats.startTime,
        successRate: this.stats.totalJobs > 0 ? 
          ((this.stats.completedJobs / this.stats.totalJobs) * 100).toFixed(2) + '%' : '0%'
      },
      settings: {
        maxConcurrent: this.maxConcurrent,
        isProcessing: this.isProcessing
      },
      jobs: {
        waiting: this.queue.map(job => ({
          id: job.id,
          type: job.type,
          priority: job.priority,
          userId: job.userId,
          createdAt: job.createdAt
        })),
        processing: this.processing.map(job => ({
          id: job.id,
          type: job.type,
          userId: job.userId,
          startedAt: job.startedAt,
          duration: new Date() - job.startedAt
        })),
        recentCompleted: this.completed.slice(-10).map(job => ({
          id: job.id,
          type: job.type,
          userId: job.userId,
          duration: job.completedAt - job.startedAt,
          completedAt: job.completedAt
        })),
        recentFailed: this.failed.slice(-10).map(job => ({
          id: job.id,
          type: job.type,
          userId: job.userId,
          retries: job.retries,
          lastError: job.lastError,
          failedAt: job.failedAt
        }))
      }
    };
  }

  /**
   * Изменить настройки очереди
   */
  updateSettings(settings) {
    if (settings.maxConcurrent && settings.maxConcurrent > 0 && settings.maxConcurrent <= 10) {
      this.maxConcurrent = settings.maxConcurrent;
      logger.info(`Максимальное количество параллельных заданий изменено на: ${this.maxConcurrent}`);
    }
    
    // Перезапускаем обработку с новыми настройками
    this.processQueue();
  }

  /**
   * Очистить завершенные и провалившиеся задания
   */
  cleanup() {
    const beforeCompleted = this.completed.length;
    const beforeFailed = this.failed.length;
    
    this.completed = [];
    this.failed = [];
    
    logger.info(`Очистка истории: удалено ${beforeCompleted} завершенных и ${beforeFailed} провалившихся заданий`);
  }

  /**
   * Генерация ID задания
   */
  generateJobId() {
    return `job_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Получить задание по ID
   */
  getJobById(jobId) {
    return [...this.queue, ...this.processing, ...this.completed, ...this.failed]
      .find(job => job.id === jobId);
  }

  /**
   * Отменить задание (если оно еще в очереди)
   */
  cancelJob(jobId) {
    const jobIndex = this.queue.findIndex(job => job.id === jobId);
    if (jobIndex !== -1) {
      const job = this.queue.splice(jobIndex, 1)[0];
      job.status = 'cancelled';
      job.cancelledAt = new Date();
      
      logger.info(`Задание отменено: ${jobId}`);
      return true;
    }
    
    return false;
  }
}

// Создаем единственный экземпляр очереди
const queueManager = new QueueManager();

module.exports = queueManager;