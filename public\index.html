<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Avatar Print Test UI</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .header p {
            opacity: 0.9;
            font-size: 1.1rem;
        }

        .content {
            padding: 40px;
        }

        .section {
            margin-bottom: 40px;
            padding: 30px;
            border: 2px solid #f0f0f0;
            border-radius: 15px;
            background: #fafafa;
        }

        .section h2 {
            color: #333;
            margin-bottom: 20px;
            font-size: 1.5rem;
        }

        .form-group {
            margin-bottom: 20px;
        }

        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #555;
        }

        input, select, button {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        input:focus, select:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            cursor: pointer;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .status-buttons {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }

        .response {
            margin-top: 20px;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }

        .response.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }

        .response.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }

        .response.info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .example-urls {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 15px;
            margin-top: 10px;
        }

        .example-urls h4 {
            color: #856404;
            margin-bottom: 10px;
        }

        .example-urls p {
            color: #856404;
            font-size: 14px;
            margin-bottom: 5px;
        }

        .url-example {
            background: white;
            padding: 8px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            word-break: break-all;
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🖨️ Avatar Print Test</h1>
            <p>Тестирование печати аватаров с imgbb</p>
        </div>

        <div class="content">
            <!-- Статус сервера -->
            <div class="section">
                <h2>📊 Статус системы</h2>
                <div class="status-buttons">
                    <button onclick="checkHealth()">
                        <span id="health-loading"></span>
                        Проверить сервер
                    </button>
                    <button onclick="checkPrinterStatus()">
                        <span id="printer-loading"></span>
                        Статус принтера
                    </button>
                </div>
                <div id="status-response"></div>
            </div>

            <!-- Список принтеров -->
            <div class="section">
                <h2>🖨️ Доступные принтеры</h2>
                <button onclick="checkPrinters()">
                    <span id="printers-loading"></span>
                    Обновить список принтеров
                </button>
                <div id="printers-response"></div>
            </div>

            <!-- Тестовая печать -->
            <div class="section">
                <h2>🧪 Тестовая печать</h2>
                <p style="margin-bottom: 20px; color: #666;">Несколько способов тестовой печати для проверки принтера</p>
                
                <!-- HTML тестовая печать -->
                <div style="margin-bottom: 20px; padding: 15px; border: 2px solid #28a745; border-radius: 8px; background: #d4edda;">
                    <h4 style="color: #155724; margin-bottom: 10px;">🖼️ HTML тестовое изображение</h4>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 15px;">
                        <div class="form-group" style="margin-bottom: 0;">
                            <label for="testFormat">Формат:</label>
                            <select id="testFormat">
                                <option value="4x6">4x6 (стандартный)</option>
                                <option value="6x8">6x8 (большой)</option>
                                <option value="5x7">5x7 (средний)</option>
                                <option value="2x6">2x6 (полоска)</option>
                            </select>
                        </div>
                        <div class="form-group" style="margin-bottom: 0;">
                            <label for="testCopies">Копий:</label>
                            <select id="testCopies">
                                <option value="1">1 копия</option>
                                <option value="2">2 копии</option>
                                <option value="3">3 копии</option>
                            </select>
                        </div>
                    </div>
                    <button onclick="printTestImage()" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%);">
                        <span id="test-loading"></span>
                        🖼️ Печать HTML тестового изображения
                    </button>
                </div>

                <!-- Простая текстовая печать -->
                <div style="margin-bottom: 20px; padding: 15px; border: 2px solid #007bff; border-radius: 8px; background: #d1ecf1;">
                    <h4 style="color: #0c5460; margin-bottom: 10px;">📝 Простая текстовая печать</h4>
                    <div style="display: grid; grid-template-columns: 2fr 1fr; gap: 15px; margin-bottom: 15px;">
                        <div class="form-group" style="margin-bottom: 0;">
                            <label for="simpleText">Текст для печати:</label>
                            <input type="text" id="simpleText" value="ТЕСТОВАЯ ПЕЧАТЬ" placeholder="Введите текст">
                        </div>
                        <div class="form-group" style="margin-bottom: 0;">
                            <label for="simpleCopies">Копий:</label>
                            <select id="simpleCopies">
                                <option value="1">1 копия</option>
                                <option value="2">2 копии</option>
                                <option value="3">3 копии</option>
                            </select>
                        </div>
                    </div>
                    <button onclick="printSimpleText()" style="background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);">
                        <span id="simple-loading"></span>
                        📝 Печать простого текста
                    </button>
                </div>

                <div id="test-response"></div>
            </div>

            <!-- Печать изображения -->
            <div class="section">
                <h2>🖼️ Универсальная печать изображений</h2>
                
                <div class="form-group">
                    <label for="imageUrl">URL изображения (любой источник):</label>
                    <input type="url" id="imageUrl" placeholder="https://example.com/image.jpg">
                    
                    <div class="example-urls">
                        <h4>Поддерживаемые источники:</h4>
                        <p>✅ <strong>imgbb.com</strong> - загрузи на <a href="https://imgbb.com" target="_blank">imgbb.com</a> и скопируй Direct link</p>
                        
                        <p>✅ <strong>Тестовые изображения (гарантированно работают):</strong></p>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px; margin: 10px 0;">
                            <button onclick="useTestUrl('https://via.placeholder.com/600x400/FF0000/FFFFFF?text=RED+TEST')" 
                                    style="background: #dc3545; color: white; padding: 8px; border: none; border-radius: 4px; font-size: 12px;">
                                🔴 Красное тестовое
                            </button>
                            <button onclick="useTestUrl('https://via.placeholder.com/600x400/00FF00/000000?text=GREEN+TEST')" 
                                    style="background: #28a745; color: white; padding: 8px; border: none; border-radius: 4px; font-size: 12px;">
                                🟢 Зеленое тестовое
                            </button>
                            <button onclick="useTestUrl('https://via.placeholder.com/600x400/0000FF/FFFFFF?text=BLUE+TEST')" 
                                    style="background: #007bff; color: white; padding: 8px; border: none; border-radius: 4px; font-size: 12px;">
                                🔵 Синее тестовое
                            </button>
                            <button onclick="useTestUrl('https://picsum.photos/600/400')" 
                                    style="background: #6c757d; color: white; padding: 8px; border: none; border-radius: 4px; font-size: 12px;">
                                📷 Случайное фото
                            </button>
                        </div>
                        
                        <p>✅ <strong>Поддерживаемые форматы:</strong> JPG, PNG, GIF, BMP, WEBP, TIFF, SVG</p>
                        
                        <p>⚠️ <strong>Важно:</strong> Для imgbb используй только Direct link (начинается с https://i.ibb.co/)</p>
                    </div>
                </div>

                <div class="form-group">
                    <label for="format">Формат печати:</label>
                    <select id="format">
                        <option value="4x6">4x6 (стандартный)</option>
                        <option value="6x8">6x8 (большой)</option>
                        <option value="5x7">5x7 (средний)</option>
                        <option value="2x6">2x6 (полоска)</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="copies">Количество копий:</label>
                    <select id="copies">
                        <option value="1">1 копия</option>
                        <option value="2">2 копии</option>
                        <option value="3">3 копии</option>
                        <option value="4">4 копии</option>
                        <option value="5">5 копий</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="userId">ID пользователя (опционально):</label>
                    <input type="text" id="userId" placeholder="user123">
                </div>

                <button onclick="printImage()">
                    <span id="print-loading"></span>
                    🖨️ Отправить на печать
                </button>

                <div id="print-response"></div>
            </div>

            <!-- Альтернативные способы печати -->
            <div class="section">
                <h2>🔧 Альтернативные способы печати</h2>
                
                <!-- Прямая печать файла -->
                <div style="margin-bottom: 30px; padding: 20px; border: 2px solid #ffc107; border-radius: 10px; background: #fff3cd;">
                    <h3 style="color: #856404; margin-bottom: 15px;">📁 Прямая печать файла</h3>
                    <div class="form-group">
                        <label for="directFilePath">Путь к файлу:</label>
                        <input type="text" id="directFilePath" placeholder="C:\path\to\image.jpg">
                    </div>
                    <div class="form-group">
                        <label for="directFormat">Формат:</label>
                        <select id="directFormat">
                            <option value="4x6">4x6</option>
                            <option value="6x8">6x8</option>
                            <option value="5x7">5x7</option>
                            <option value="2x6">2x6</option>
                        </select>
                    </div>
                    <button onclick="printDirectFile()" style="background: linear-gradient(135deg, #ffc107 0%, #ff8c00 100%);">
                        <span id="direct-loading"></span>
                        📁 Печать файла напрямую
                    </button>
                    <div id="direct-response"></div>
                </div>

                <!-- Системная команда -->
                <div style="margin-bottom: 30px; padding: 20px; border: 2px solid #dc3545; border-radius: 10px; background: #f8d7da;">
                    <h3 style="color: #721c24; margin-bottom: 15px;">⚡ Системная команда печати</h3>
                    <div class="form-group">
                        <label for="systemCommand">Команда:</label>
                        <input type="text" id="systemCommand" placeholder='powershell -Command "Get-Printer"'>
                    </div>
                    <div class="form-group">
                        <label for="commandDescription">Описание:</label>
                        <input type="text" id="commandDescription" placeholder="Проверка принтеров">
                    </div>
                    <button onclick="executeSystemCommand()" style="background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);">
                        <span id="command-loading"></span>
                        ⚡ Выполнить команду
                    </button>
                    <div id="command-response"></div>
                </div>

                <!-- Быстрые команды -->
                <div style="margin-bottom: 30px; padding: 20px; border: 2px solid #17a2b8; border-radius: 10px; background: #d1ecf1;">
                    <h3 style="color: #0c5460; margin-bottom: 15px;">🚀 Быстрые команды</h3>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px;">
                        <button onclick="quickCommand('Get-Printer | Select-Object Name, PrinterStatus')" 
                                style="background: linear-gradient(135deg, #17a2b8 0%, #138496 100%); font-size: 14px;">
                            📋 Список принтеров
                        </button>
                        <button onclick="quickCommand('Get-PrintJob')" 
                                style="background: linear-gradient(135deg, #17a2b8 0%, #138496 100%); font-size: 14px;">
                            📄 Задания печати
                        </button>
                        <button onclick="quickCommand('Test-NetConnection google.com -Port 80')" 
                                style="background: linear-gradient(135deg, #17a2b8 0%, #138496 100%); font-size: 14px;">
                            🌐 Тест интернета
                        </button>
                        <button onclick="quickCommand('Get-Process | Where-Object {$_.ProcessName -like \"*print*\"}')" 
                                style="background: linear-gradient(135deg, #17a2b8 0%, #138496 100%); font-size: 14px;">
                            🔍 Процессы печати
                        </button>
                    </div>
                    <div id="quick-response"></div>
                </div>
            </div>

            <!-- Управление очередью печати -->
            <div class="section">
                <h2>📋 Управление очередью печати</h2>
                
                <!-- Статус очереди -->
                <div style="margin-bottom: 20px; padding: 15px; border: 2px solid #17a2b8; border-radius: 8px; background: #d1ecf1;">
                    <h4 style="color: #0c5460; margin-bottom: 10px;">📊 Статус очереди</h4>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 15px;">
                        <button onclick="checkQueueStatus()" style="background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);">
                            <span id="queue-status-loading"></span>
                            📊 Статус очереди
                        </button>
                        <button onclick="checkQueue()" style="background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);">
                            <span id="queue-loading"></span>
                            📋 Старая очередь
                        </button>
                    </div>
                    <div id="queue-status-response"></div>
                </div>

                <!-- Настройки очереди -->
                <div style="margin-bottom: 20px; padding: 15px; border: 2px solid #ffc107; border-radius: 8px; background: #fff3cd;">
                    <h4 style="color: #856404; margin-bottom: 10px;">⚙️ Настройки очереди</h4>
                    <div style="display: grid; grid-template-columns: 2fr 1fr; gap: 15px; margin-bottom: 15px;">
                        <div class="form-group" style="margin-bottom: 0;">
                            <label for="maxConcurrent">Максимум параллельных заданий:</label>
                            <select id="maxConcurrent">
                                <option value="1">1 задание</option>
                                <option value="2" selected>2 задания</option>
                                <option value="3">3 задания</option>
                                <option value="4">4 задания</option>
                                <option value="5">5 заданий</option>
                            </select>
                        </div>
                        <div style="display: flex; align-items: end;">
                            <button onclick="updateQueueSettings()" style="background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%); width: 100%;">
                                <span id="settings-loading"></span>
                                ⚙️ Применить
                            </button>
                        </div>
                    </div>
                    <div id="settings-response"></div>
                </div>

                <!-- Управление -->
                <div style="margin-bottom: 20px; padding: 15px; border: 2px solid #dc3545; border-radius: 8px; background: #f8d7da;">
                    <h4 style="color: #721c24; margin-bottom: 10px;">🧹 Управление</h4>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 15px;">
                        <button onclick="cleanupQueue()" style="background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);">
                            <span id="cleanup-loading"></span>
                            🧹 Очистить историю
                        </button>
                        <button onclick="autoRefreshToggle()" id="auto-refresh-btn" style="background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);">
                            🔄 Авто-обновление: ВЫКЛ
                        </button>
                    </div>
                    <div id="cleanup-response"></div>
                </div>

                <div id="queue-response"></div>
            </div>
        </div>
    </div>

    <script src="app.js"></script>
</body>
</html>