# Moscow 2030 Avatar Backend - Скрипт установки
# Запускать от имени администратора

Write-Host "🚀 Установка Moscow 2030 Avatar Backend..." -ForegroundColor Green

# Проверка прав администратора
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "❌ Запустите PowerShell от имени администратора!" -ForegroundColor Red
    exit 1
}

# Создание папок
Write-Host "📁 Создание папок..." -ForegroundColor Yellow
$appPath = "C:\apps\moscow2030-avatar-backend"
New-Item -ItemType Directory -Force -Path $appPath
New-Item -ItemType Directory -Force -Path "$appPath\logs"
New-Item -ItemType Directory -Force -Path "$appPath\temp"

# Установка Node.js (если не установлен)
Write-Host "📦 Проверка Node.js..." -ForegroundColor Yellow
try {
    $nodeVersion = node --version
    Write-Host "✅ Node.js уже установлен: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Node.js не найден. Установите с https://nodejs.org/" -ForegroundColor Red
    exit 1
}

# Установка PM2
Write-Host "📦 Установка PM2..." -ForegroundColor Yellow
npm install -g pm2
npm install -g pm2-windows-service

# Клонирование репозитория
Write-Host "📥 Клонирование репозитория..." -ForegroundColor Yellow
Set-Location $appPath
if (Test-Path ".git") {
    git pull origin main
} else {
    git clone https://github.com/Den999/moscow2030-avatar-backend.git .
}

# Установка зависимостей
Write-Host "📦 Установка зависимостей..." -ForegroundColor Yellow
npm install --production

# Создание .env файла
Write-Host "⚙️ Создание конфигурации..." -ForegroundColor Yellow
if (-not (Test-Path ".env.production")) {
    Copy-Item ".env.example" ".env.production"
    Write-Host "✅ Создан .env.production - настройте его перед запуском!" -ForegroundColor Green
}

# Создание PM2 конфигурации
Write-Host "⚙️ Создание PM2 конфигурации..." -ForegroundColor Yellow
$pm2Config = @"
module.exports = {
  apps: [{
    name: 'moscow2030-avatar-backend',
    script: 'server.js',
    cwd: '$appPath',
    env_production: {
      NODE_ENV: 'production',
      PORT: 3002
    },
    instances: 1,
    exec_mode: 'fork',
    watch: false,
    max_memory_restart: '500M',
    error_file: 'logs/pm2-error.log',
    out_file: 'logs/pm2-out.log',
    log_file: 'logs/pm2-combined.log',
    time: true,
    autorestart: true,
    max_restarts: 10,
    min_uptime: '10s',
    restart_delay: 5000
  }]
};
"@

$pm2Config | Out-File -FilePath "ecosystem.config.js" -Encoding UTF8

# Установка PM2 как службы Windows
Write-Host "🔧 Настройка PM2 как службы Windows..." -ForegroundColor Yellow
pm2-service-install

# Настройка Firewall
Write-Host "🔥 Настройка Firewall..." -ForegroundColor Yellow
try {
    New-NetFirewallRule -DisplayName "HTTP Moscow2030" -Direction Inbound -Protocol TCP -LocalPort 80 -Action Allow -ErrorAction SilentlyContinue
    New-NetFirewallRule -DisplayName "HTTPS Moscow2030" -Direction Inbound -Protocol TCP -LocalPort 443 -Action Allow -ErrorAction SilentlyContinue
    Write-Host "✅ Firewall настроен" -ForegroundColor Green
} catch {
    Write-Host "⚠️ Не удалось настроить Firewall автоматически" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "🎉 Установка завершена!" -ForegroundColor Green
Write-Host ""
Write-Host "📋 Следующие шаги:" -ForegroundColor Cyan
Write-Host "1. Настройте .env.production файл" -ForegroundColor White
Write-Host "2. Установите и настройте nginx" -ForegroundColor White
Write-Host "3. Запустите: pm2 start ecosystem.config.js --env production" -ForegroundColor White
Write-Host "4. Сохраните конфигурацию: pm2 save" -ForegroundColor White
Write-Host ""
Write-Host "📖 Полная инструкция в DEPLOYMENT.md" -ForegroundColor Cyan